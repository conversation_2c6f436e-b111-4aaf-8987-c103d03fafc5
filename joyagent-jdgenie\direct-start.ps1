# JoyAgent-JDGenie 直接启动脚本
Write-Host "🚀 JoyAgent-JDGenie 直接启动" -ForegroundColor Blue
Write-Host "=================================="

# 检查Docker状态
Write-Host "检查Docker状态..." -ForegroundColor Yellow
docker info *>$null 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Docker未运行，请先启动Docker Desktop" -ForegroundColor Red
    Read-Host "按回车键退出..."
    exit 1
}
Write-Host "✅ Docker运行正常" -ForegroundColor Green

# 检查docker-compose.yml
Write-Host "检查配置文件..." -ForegroundColor Yellow
if (-not (Test-Path "docker-compose.yml")) {
    Write-Host "❌ docker-compose.yml文件未找到" -ForegroundColor Red
    Read-Host "按回车键退出..."
    exit 1
}
Write-Host "✅ 配置文件存在" -ForegroundColor Green

# 启动服务
Write-Host "启动JoyAgent-JDGenie服务..." -ForegroundColor Yellow
docker-compose up -d --build

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 服务启动成功" -ForegroundColor Green
} else {
    Write-Host "❌ 服务启动失败" -ForegroundColor Red
    Write-Host "查看详细日志: docker-compose logs -f" -ForegroundColor Cyan
    Read-Host "按回车键退出..."
    exit 1
}

# 显示服务状态
Write-Host ""
Write-Host "服务状态:" -ForegroundColor Blue
docker-compose ps

Write-Host ""
Write-Host "=================================="
Write-Host "🎉 JoyAgent-JDGenie 启动完成！" -ForegroundColor Green
Write-Host "=================================="

Write-Host "🌐 访问地址：" -ForegroundColor Blue
Write-Host "• 主界面: http://localhost:3000" -ForegroundColor Cyan
Write-Host "• 后端API: http://localhost:8080" -ForegroundColor Cyan
Write-Host "• 工具服务: http://localhost:1601" -ForegroundColor Cyan
Write-Host "• MCP客户端: http://localhost:8188" -ForegroundColor Cyan

Write-Host ""
Write-Host "💡 常用命令：" -ForegroundColor Blue
Write-Host "• 查看日志: docker-compose logs -f" -ForegroundColor Cyan
Write-Host "• 停止服务: docker-compose down" -ForegroundColor Cyan
Write-Host "• 重启服务: docker-compose restart" -ForegroundColor Cyan

$choice = Read-Host "`n是否打开浏览器？(Y/N)"
if ($choice -eq "Y" -or $choice -eq "y") {
    Start-Process "http://localhost:3000"
}

Read-Host "按回车键退出..."
