# 中国大陆镜像源配置脚本
Write-Host "🇨🇳 中国大陆镜像源配置脚本" -ForegroundColor Blue
Write-Host "=================================="

# 第1步：配置Docker镜像源
Write-Host "第1步：配置Docker镜像源..." -ForegroundColor Yellow

$dockerConfigDir = "$env:USERPROFILE\.docker"
if (-not (Test-Path $dockerConfigDir)) {
    New-Item -ItemType Directory -Path $dockerConfigDir -Force
}

$daemonConfig = @{
    "registry-mirrors" = @(
        "https://docker.m.daocloud.io",
        "https://dockerproxy.com",
        "https://mirror.baidubce.com",
        "https://reg-mirror.qiniu.com"
    )
    "insecure-registries" = @()
    "debug" = $false
    "experimental" = $false
    "builder" = @{
        "gc" = @{
            "defaultKeepStorage" = "20GB"
            "enabled" = $true
        }
    }
}

$daemonConfigPath = "$dockerConfigDir\daemon.json"
$daemonConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $daemonConfigPath -Encoding UTF8

Write-Host "✅ Docker镜像源配置完成" -ForegroundColor Green
Write-Host "配置文件位置: $daemonConfigPath" -ForegroundColor Cyan

# 第2步：下载WSL（使用国内镜像）
Write-Host ""
Write-Host "第2步：下载WSL（使用国内镜像）..." -ForegroundColor Yellow

$wslMirrors = @(
    @{Name="清华大学"; Url="https://mirrors.tuna.tsinghua.edu.cn/github-release/microsoft/WSL/LatestRelease/Microsoft.WSL_2.0.14.0_x64_ARM64.msixbundle"},
    @{Name="中科大"; Url="https://mirrors.ustc.edu.cn/github-release/microsoft/WSL/releases/latest/download/Microsoft.WSL_2.0.14.0_x64_ARM64.msixbundle"},
    @{Name="华为云"; Url="https://mirrors.huaweicloud.com/github-release/microsoft/WSL/releases/latest/download/Microsoft.WSL_2.0.14.0_x64_ARM64.msixbundle"}
)

$wslDownloaded = $false

foreach ($mirror in $wslMirrors) {
    Write-Host "尝试从 $($mirror.Name) 下载WSL..." -ForegroundColor Cyan
    try {
        Invoke-WebRequest -Uri $mirror.Url -OutFile "WSL_latest.msixbundle" -TimeoutSec 30
        if (Test-Path "WSL_latest.msixbundle") {
            $fileSize = (Get-Item "WSL_latest.msixbundle").Length
            if ($fileSize -gt 1MB) {
                Write-Host "✅ 从 $($mirror.Name) 下载成功 (大小: $([math]::Round($fileSize/1MB, 2))MB)" -ForegroundColor Green
                $wslDownloaded = $true
                break
            } else {
                Write-Host "❌ 文件大小异常，删除重试" -ForegroundColor Red
                Remove-Item "WSL_latest.msixbundle" -Force
            }
        }
    } catch {
        Write-Host "❌ 从 $($mirror.Name) 下载失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

if ($wslDownloaded) {
    Write-Host ""
    Write-Host "第3步：安装WSL..." -ForegroundColor Yellow
    try {
        Add-AppxPackage -Path "WSL_latest.msixbundle"
        Write-Host "✅ WSL安装成功" -ForegroundColor Green
    } catch {
        Write-Host "❌ WSL安装失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ 所有镜像源下载失败" -ForegroundColor Red
    Write-Host "💡 建议使用Microsoft Store更新WSL" -ForegroundColor Yellow
}

# 第4步：配置Ubuntu镜像源（预配置）
Write-Host ""
Write-Host "第4步：准备Ubuntu镜像源配置..." -ForegroundColor Yellow

$ubuntuSourcesList = @"
# 阿里云镜像源
deb http://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ jammy-security main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ jammy-updates main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ jammy-backports main restricted universe multiverse

# 清华大学镜像源（备用）
# deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy main restricted universe multiverse
# deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy-updates main restricted universe multiverse
# deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy-backports main restricted universe multiverse
# deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy-security main restricted universe multiverse
"@

$ubuntuSourcesList | Out-File -FilePath "ubuntu-sources.list" -Encoding UTF8
Write-Host "✅ Ubuntu镜像源配置文件已准备" -ForegroundColor Green
Write-Host "文件位置: $(Get-Location)\ubuntu-sources.list" -ForegroundColor Cyan

# 第5步：创建Ubuntu配置脚本
Write-Host ""
Write-Host "第5步：创建Ubuntu配置脚本..." -ForegroundColor Yellow

$ubuntuSetupScript = @"
#!/bin/bash
# Ubuntu镜像源配置脚本

echo "配置Ubuntu镜像源..."

# 备份原始sources.list
sudo cp /etc/apt/sources.list /etc/apt/sources.list.backup

# 使用阿里云镜像源
sudo tee /etc/apt/sources.list > /dev/null <<EOF
# 阿里云镜像源
deb http://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ jammy-security main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ jammy-updates main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ jammy-backports main restricted universe multiverse
EOF

# 更新软件包列表
sudo apt update

echo "Ubuntu镜像源配置完成！"
"@

$ubuntuSetupScript | Out-File -FilePath "setup-ubuntu-mirrors.sh" -Encoding UTF8
Write-Host "✅ Ubuntu配置脚本已创建" -ForegroundColor Green
Write-Host "文件位置: $(Get-Location)\setup-ubuntu-mirrors.sh" -ForegroundColor Cyan

# 第6步：重启Docker Desktop
Write-Host ""
Write-Host "第6步：重启Docker Desktop以应用镜像源..." -ForegroundColor Yellow

try {
    # 停止Docker Desktop
    Get-Process "Docker Desktop" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 5
    
    # 启动Docker Desktop
    Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    Write-Host "✅ Docker Desktop已重启" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Desktop重启失败" -ForegroundColor Red
}

# 第7步：等待Docker启动并测试镜像源
Write-Host ""
Write-Host "第7步：测试Docker镜像源..." -ForegroundColor Yellow
Write-Host "等待Docker启动..."

$maxAttempts = 24
$attempt = 0

while ($attempt -lt $maxAttempts) {
    $attempt++
    Write-Host "检查Docker状态... ($attempt/$maxAttempts)" -NoNewline
    
    try {
        docker info *>$null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host " ✅" -ForegroundColor Green
            break
        }
    } catch {}
    
    Write-Host " ⏳" -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

if ($attempt -lt $maxAttempts) {
    Write-Host ""
    Write-Host "测试Docker镜像源..."
    try {
        # 测试拉取一个小镜像
        docker pull hello-world
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker镜像源工作正常" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Docker镜像拉取可能较慢，但配置已完成" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ Docker镜像测试失败，但配置已完成" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "=================================="
Write-Host "🎉 中国大陆镜像源配置完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 已配置的镜像源：" -ForegroundColor Blue
Write-Host "• Docker: 道客云、百度云、七牛云等" -ForegroundColor Cyan
Write-Host "• Ubuntu: 阿里云镜像源" -ForegroundColor Cyan
Write-Host ""
Write-Host "📂 配置文件位置：" -ForegroundColor Blue
Write-Host "• Docker: $daemonConfigPath" -ForegroundColor Cyan
Write-Host "• Ubuntu: $(Get-Location)\ubuntu-sources.list" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 下一步：运行 docker-start.sh 启动JoyAgent-JDGenie" -ForegroundColor Blue
Write-Host "=================================="

Read-Host "按回车键继续..."
