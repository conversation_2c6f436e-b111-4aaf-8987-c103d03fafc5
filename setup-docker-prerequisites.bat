@echo off
echo 🔧 Docker Desktop 前置条件安装脚本
echo ==================================

echo 1. 更新WSL内核...
wsl --update
if %errorlevel% neq 0 (
    echo ❌ WSL更新失败
    pause
    exit /b 1
)

echo.
echo 2. 安装Ubuntu WSL发行版...
wsl --install -d Ubuntu
if %errorlevel% neq 0 (
    echo ⚠️ Ubuntu安装可能需要重启
)

echo.
echo 3. 检查WSL状态...
wsl --status

echo.
echo 4. 检查已安装的发行版...
wsl --list --verbose

echo.
echo 5. 设置WSL 2为默认版本...
wsl --set-default-version 2

echo.
echo ==================================
echo ✅ WSL配置完成！
echo.
echo 💡 下一步操作：
echo 1. 如果提示需要重启，请重启计算机
echo 2. 重启后运行Docker Desktop
echo 3. 然后运行: docker-start.sh
echo ==================================
pause
