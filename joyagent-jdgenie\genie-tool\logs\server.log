2025-08-01 12:34:04.367 INFO log_util.__aenter__ 97763327-9d76-4e25-8c98-a8b265c6e73f GET / start...
2025-08-01 12:34:04.368 INFO log_util.__aexit__ 97763327-9d76-4e25-8c98-a8b265c6e73f GET / cost=[0 ms]
2025-08-01 12:36:13.897 INFO log_util.__aenter__ 3fcd0aed-6398-4741-8473-ec288e0feb39 GET / start...
2025-08-01 12:36:13.898 INFO log_util.__aexit__ 3fcd0aed-6398-4741-8473-ec288e0feb39 GET / cost=[1 ms]
2025-08-01 16:20:30.767 INFO log_util.__aenter__ e795ec95-1424-42ea-a8de-dd0738ad4a8f GET / start...
2025-08-01 16:20:30.768 INFO log_util.__aexit__ e795ec95-1424-42ea-a8de-dd0738ad4a8f GET / cost=[1 ms]
2025-08-01 16:31:19.662 INFO log_util.__aenter__ c95aba6f-e753-45d0-898b-f036326052b0 GET /health start...
2025-08-01 16:31:19.663 INFO log_util.__aexit__ c95aba6f-e753-45d0-898b-f036326052b0 GET /health cost=[1 ms]
2025-08-01 16:31:28.536 INFO log_util.__aenter__ 083a453f-39d6-4dbb-8e3a-75f7b0d58946 GET / start...
2025-08-01 16:31:28.537 INFO log_util.__aexit__ 083a453f-39d6-4dbb-8e3a-75f7b0d58946 GET / cost=[1 ms]
2025-08-01 16:31:56.801 INFO log_util.__aenter__ 2c430bc5-9f18-4e4f-a232-a21b0490b20c GET /docs start...
2025-08-01 16:31:56.802 INFO log_util.__aexit__ 2c430bc5-9f18-4e4f-a232-a21b0490b20c GET /docs cost=[0 ms]
2025-08-01 17:02:19.343 INFO log_util.__aenter__ f31a1e79-59f9-411d-bc26-98fc5824acbb GET /docs start...
2025-08-01 17:02:19.344 INFO log_util.__aexit__ f31a1e79-59f9-411d-bc26-98fc5824acbb GET /docs cost=[0 ms]
2025-08-01 17:03:46.587 INFO log_util.__aenter__ 53306004-cd7f-45e8-86fb-f39e865eb5ac POST /v1/tool/deepsearch start...
2025-08-01 17:03:46.587 INFO log_util.__aenter__ 0134d767-bd22-4b9b-94d7-21d41b55eb31 POST /v1/tool/deepsearch start...
2025-08-01 17:03:46.587 INFO log_util.__aenter__ 91e24c36-43d8-49f2-acb9-54f809437048 POST /v1/tool/deepsearch start...
2025-08-01 17:03:46.589 INFO middleware_util.custom_route_handler 53306004-cd7f-45e8-86fb-f39e865eb5ac POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"京东 2023年 财报 公开数据","request_id":"geniesession-1754038978598-53:1754038978618-3613:aym94","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:03:46.592 INFO middleware_util.custom_route_handler 0134d767-bd22-4b9b-94d7-21d41b55eb31 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"京东 2024年 财报 公开数据","request_id":"geniesession-1754038978598-53:1754038978618-3613:vgop5","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:03:46.593 INFO middleware_util.custom_route_handler 91e24c36-43d8-49f2-acb9-54f809437048 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"京东 2025年 财报 公开数据","request_id":"geniesession-1754038978598-53:1754038978618-3613:t10nh","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:03:46.593 INFO log_util.__enter__ 53306004-cd7f-45e8-86fb-f39e865eb5ac  run start...
2025-08-01 17:03:46.594 INFO log_util.__exit__ 53306004-cd7f-45e8-86fb-f39e865eb5ac  run cost=[0 ms]
2025-08-01 17:03:46.594 INFO deepsearch.run geniesession-1754038978598-53:1754038978618-3613:aym94 第 1 轮深度搜索...
2025-08-01 17:03:46.594 INFO log_util.__aenter__ 53306004-cd7f-45e8-86fb-f39e865eb5ac  query_decompose start...
2025-08-01 17:03:46.598 ERROR log_util.__aexit__ 53306004-cd7f-45e8-86fb-f39e865eb5ac  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:03:46.599 INFO log_util.__enter__ 0134d767-bd22-4b9b-94d7-21d41b55eb31  run start...
2025-08-01 17:03:46.599 INFO log_util.__exit__ 0134d767-bd22-4b9b-94d7-21d41b55eb31  run cost=[0 ms]
2025-08-01 17:03:46.599 INFO deepsearch.run geniesession-1754038978598-53:1754038978618-3613:vgop5 第 1 轮深度搜索...
2025-08-01 17:03:46.599 INFO log_util.__aenter__ 0134d767-bd22-4b9b-94d7-21d41b55eb31  query_decompose start...
2025-08-01 17:03:46.600 ERROR log_util.__aexit__ 0134d767-bd22-4b9b-94d7-21d41b55eb31  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:03:46.601 INFO log_util.__enter__ 91e24c36-43d8-49f2-acb9-54f809437048  run start...
2025-08-01 17:03:46.601 INFO log_util.__exit__ 91e24c36-43d8-49f2-acb9-54f809437048  run cost=[0 ms]
2025-08-01 17:03:46.601 INFO deepsearch.run geniesession-1754038978598-53:1754038978618-3613:t10nh 第 1 轮深度搜索...
2025-08-01 17:03:46.601 INFO log_util.__aenter__ 91e24c36-43d8-49f2-acb9-54f809437048  query_decompose start...
2025-08-01 17:03:46.602 ERROR log_util.__aexit__ 91e24c36-43d8-49f2-acb9-54f809437048  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:03:46.603 INFO log_util.__aexit__ 53306004-cd7f-45e8-86fb-f39e865eb5ac POST /v1/tool/deepsearch cost=[16 ms]
2025-08-01 17:03:46.604 INFO log_util.__aexit__ 0134d767-bd22-4b9b-94d7-21d41b55eb31 POST /v1/tool/deepsearch cost=[17 ms]
2025-08-01 17:03:46.604 INFO log_util.__aexit__ 91e24c36-43d8-49f2-acb9-54f809437048 POST /v1/tool/deepsearch cost=[17 ms]
2025-08-01 17:03:57.436 INFO log_util.__aenter__ 141c0aa9-ca9f-4c24-8d28-fe2f1fd8a93e POST /v1/tool/deepsearch start...
2025-08-01 17:03:57.437 INFO log_util.__aenter__ 94ef83be-ff17-41de-b9d2-de35e966a6d1 POST /v1/tool/deepsearch start...
2025-08-01 17:03:57.438 INFO log_util.__aenter__ 5b35d3df-a5b1-4eb9-942a-bc8ee848e68a POST /v1/tool/deepsearch start...
2025-08-01 17:03:57.439 INFO middleware_util.custom_route_handler 141c0aa9-ca9f-4c24-8d28-fe2f1fd8a93e POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"京东 财报 2024 财经新闻","request_id":"geniesession-1754038978598-53:1754038978618-3613:f8eay","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:03:57.439 INFO log_util.__enter__ 141c0aa9-ca9f-4c24-8d28-fe2f1fd8a93e  run start...
2025-08-01 17:03:57.440 INFO log_util.__exit__ 141c0aa9-ca9f-4c24-8d28-fe2f1fd8a93e  run cost=[0 ms]
2025-08-01 17:03:57.440 INFO deepsearch.run geniesession-1754038978598-53:1754038978618-3613:f8eay 第 1 轮深度搜索...
2025-08-01 17:03:57.440 INFO log_util.__aenter__ 141c0aa9-ca9f-4c24-8d28-fe2f1fd8a93e  query_decompose start...
2025-08-01 17:03:57.441 ERROR log_util.__aexit__ 141c0aa9-ca9f-4c24-8d28-fe2f1fd8a93e  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:03:57.442 INFO middleware_util.custom_route_handler 94ef83be-ff17-41de-b9d2-de35e966a6d1 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"京东 投资者关系 财报 2025","request_id":"geniesession-1754038978598-53:1754038978618-3613:cy7un","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:03:57.443 INFO middleware_util.custom_route_handler 5b35d3df-a5b1-4eb9-942a-bc8ee848e68a POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"京东 2023年财报 官方发布","request_id":"geniesession-1754038978598-53:1754038978618-3613:w9ocx","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:03:57.443 INFO log_util.__enter__ 94ef83be-ff17-41de-b9d2-de35e966a6d1  run start...
2025-08-01 17:03:57.444 INFO log_util.__exit__ 94ef83be-ff17-41de-b9d2-de35e966a6d1  run cost=[0 ms]
2025-08-01 17:03:57.444 INFO deepsearch.run geniesession-1754038978598-53:1754038978618-3613:cy7un 第 1 轮深度搜索...
2025-08-01 17:03:57.444 INFO log_util.__aenter__ 94ef83be-ff17-41de-b9d2-de35e966a6d1  query_decompose start...
2025-08-01 17:03:57.445 ERROR log_util.__aexit__ 94ef83be-ff17-41de-b9d2-de35e966a6d1  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:03:57.445 INFO log_util.__enter__ 5b35d3df-a5b1-4eb9-942a-bc8ee848e68a  run start...
2025-08-01 17:03:57.445 INFO log_util.__exit__ 5b35d3df-a5b1-4eb9-942a-bc8ee848e68a  run cost=[0 ms]
2025-08-01 17:03:57.446 INFO deepsearch.run geniesession-1754038978598-53:1754038978618-3613:w9ocx 第 1 轮深度搜索...
2025-08-01 17:03:57.446 INFO log_util.__aenter__ 5b35d3df-a5b1-4eb9-942a-bc8ee848e68a  query_decompose start...
2025-08-01 17:03:57.447 ERROR log_util.__aexit__ 5b35d3df-a5b1-4eb9-942a-bc8ee848e68a  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:03:57.448 INFO log_util.__aexit__ 141c0aa9-ca9f-4c24-8d28-fe2f1fd8a93e POST /v1/tool/deepsearch cost=[12 ms]
2025-08-01 17:03:57.448 INFO log_util.__aexit__ 94ef83be-ff17-41de-b9d2-de35e966a6d1 POST /v1/tool/deepsearch cost=[11 ms]
2025-08-01 17:03:57.449 INFO log_util.__aexit__ 5b35d3df-a5b1-4eb9-942a-bc8ee848e68a POST /v1/tool/deepsearch cost=[11 ms]
2025-08-01 17:04:08.126 INFO log_util.__aenter__ e05cc96b-4dd9-4de4-be42-7b0ee49f1685 POST /v1/tool/deepsearch start...
2025-08-01 17:04:08.127 INFO log_util.__aenter__ 201c2e20-703f-4c2a-9322-2c68b980491e POST /v1/tool/deepsearch start...
2025-08-01 17:04:08.127 INFO middleware_util.custom_route_handler e05cc96b-4dd9-4de4-be42-7b0ee49f1685 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"京东 投资者关系 财报下载","request_id":"geniesession-1754038978598-53:1754038978618-3613:ifmw2","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:04:08.128 INFO middleware_util.custom_route_handler 201c2e20-703f-4c2a-9322-2c68b980491e POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"京东 2025年财报 官方发布 投资者关系","request_id":"geniesession-1754038978598-53:1754038978618-3613:qbpdp","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:04:08.129 INFO log_util.__enter__ e05cc96b-4dd9-4de4-be42-7b0ee49f1685  run start...
2025-08-01 17:04:08.129 INFO log_util.__exit__ e05cc96b-4dd9-4de4-be42-7b0ee49f1685  run cost=[0 ms]
2025-08-01 17:04:08.129 INFO deepsearch.run geniesession-1754038978598-53:1754038978618-3613:ifmw2 第 1 轮深度搜索...
2025-08-01 17:04:08.129 INFO log_util.__aenter__ e05cc96b-4dd9-4de4-be42-7b0ee49f1685  query_decompose start...
2025-08-01 17:04:08.130 ERROR log_util.__aexit__ e05cc96b-4dd9-4de4-be42-7b0ee49f1685  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:04:08.131 INFO log_util.__enter__ 201c2e20-703f-4c2a-9322-2c68b980491e  run start...
2025-08-01 17:04:08.131 INFO log_util.__exit__ 201c2e20-703f-4c2a-9322-2c68b980491e  run cost=[0 ms]
2025-08-01 17:04:08.132 INFO deepsearch.run geniesession-1754038978598-53:1754038978618-3613:qbpdp 第 1 轮深度搜索...
2025-08-01 17:04:08.132 INFO log_util.__aenter__ 201c2e20-703f-4c2a-9322-2c68b980491e  query_decompose start...
2025-08-01 17:04:08.133 ERROR log_util.__aexit__ 201c2e20-703f-4c2a-9322-2c68b980491e  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:04:08.134 INFO log_util.__aexit__ e05cc96b-4dd9-4de4-be42-7b0ee49f1685 POST /v1/tool/deepsearch cost=[7 ms]
2025-08-01 17:04:08.134 INFO log_util.__aexit__ 201c2e20-703f-4c2a-9322-2c68b980491e POST /v1/tool/deepsearch cost=[6 ms]
2025-08-01 17:07:22.889 INFO log_util.__aenter__ 6f4550d1-9809-4454-af9b-c514dd380be4 GET / start...
2025-08-01 17:07:22.889 INFO log_util.__aexit__ 6f4550d1-9809-4454-af9b-c514dd380be4 GET / cost=[0 ms]
2025-08-01 17:07:22.929 INFO log_util.__aenter__ f522a1df-bac4-4958-a3e0-4caa1200dfbc GET /favicon.ico start...
2025-08-01 17:07:22.930 INFO log_util.__aexit__ f522a1df-bac4-4958-a3e0-4caa1200dfbc GET /favicon.ico cost=[0 ms]
2025-08-01 17:12:28.538 INFO log_util.__aenter__ 55494914-024b-499b-bf97-e168bf940253 POST /v1/tool/deepsearch start...
2025-08-01 17:12:28.541 INFO middleware_util.custom_route_handler 55494914-024b-499b-bf97-e168bf940253 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2025年8月1日福州各地大雨情况","request_id":"geniesession-1754039541230-8451:1754039541252-1103:l6y8s","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:12:28.542 INFO log_util.__enter__ 55494914-024b-499b-bf97-e168bf940253  run start...
2025-08-01 17:12:28.542 INFO log_util.__exit__ 55494914-024b-499b-bf97-e168bf940253  run cost=[0 ms]
2025-08-01 17:12:28.542 INFO deepsearch.run geniesession-1754039541230-8451:1754039541252-1103:l6y8s 第 1 轮深度搜索...
2025-08-01 17:12:28.542 INFO log_util.__aenter__ 55494914-024b-499b-bf97-e168bf940253  query_decompose start...
2025-08-01 17:12:28.544 ERROR log_util.__aexit__ 55494914-024b-499b-bf97-e168bf940253  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:12:28.545 INFO log_util.__aexit__ 55494914-024b-499b-bf97-e168bf940253 POST /v1/tool/deepsearch cost=[6 ms]
2025-08-01 17:12:38.175 INFO log_util.__aenter__ 6fa368e6-a38e-44a3-99aa-25e6d6514de0 POST /v1/tool/deepsearch start...
2025-08-01 17:12:38.176 INFO middleware_util.custom_route_handler 6fa368e6-a38e-44a3-99aa-25e6d6514de0 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"福州市气象局2025年8月1日天气预警","request_id":"geniesession-1754039541230-8451:1754039541252-1103:t5kt7","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:12:38.177 INFO log_util.__enter__ 6fa368e6-a38e-44a3-99aa-25e6d6514de0  run start...
2025-08-01 17:12:38.177 INFO log_util.__exit__ 6fa368e6-a38e-44a3-99aa-25e6d6514de0  run cost=[0 ms]
2025-08-01 17:12:38.177 INFO deepsearch.run geniesession-1754039541230-8451:1754039541252-1103:t5kt7 第 1 轮深度搜索...
2025-08-01 17:12:38.178 INFO log_util.__aenter__ 6fa368e6-a38e-44a3-99aa-25e6d6514de0  query_decompose start...
2025-08-01 17:12:38.179 ERROR log_util.__aexit__ 6fa368e6-a38e-44a3-99aa-25e6d6514de0  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:12:38.180 INFO log_util.__aexit__ 6fa368e6-a38e-44a3-99aa-25e6d6514de0 POST /v1/tool/deepsearch cost=[4 ms]
2025-08-01 17:12:48.132 INFO log_util.__aenter__ 2cbb8213-66ce-47fd-8c88-04a5a59f8008 POST /v1/tool/deepsearch start...
2025-08-01 17:12:48.132 INFO middleware_util.custom_route_handler 2cbb8213-66ce-47fd-8c88-04a5a59f8008 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"福州今日暴雨预警 2025年8月1日","request_id":"geniesession-1754039541230-8451:1754039541252-1103:6v4e3","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:12:48.133 INFO log_util.__enter__ 2cbb8213-66ce-47fd-8c88-04a5a59f8008  run start...
2025-08-01 17:12:48.134 INFO log_util.__exit__ 2cbb8213-66ce-47fd-8c88-04a5a59f8008  run cost=[0 ms]
2025-08-01 17:12:48.134 INFO deepsearch.run geniesession-1754039541230-8451:1754039541252-1103:6v4e3 第 1 轮深度搜索...
2025-08-01 17:12:48.134 INFO log_util.__aenter__ 2cbb8213-66ce-47fd-8c88-04a5a59f8008  query_decompose start...
2025-08-01 17:12:48.135 ERROR log_util.__aexit__ 2cbb8213-66ce-47fd-8c88-04a5a59f8008  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:12:48.136 INFO log_util.__aexit__ 2cbb8213-66ce-47fd-8c88-04a5a59f8008 POST /v1/tool/deepsearch cost=[3 ms]
2025-08-01 17:12:56.888 INFO log_util.__aenter__ 9a69ae03-0fb6-4d35-8db0-38baafea48e0 POST /v1/tool/deepsearch start...
2025-08-01 17:12:56.889 INFO middleware_util.custom_route_handler 9a69ae03-0fb6-4d35-8db0-38baafea48e0 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"福州市气象局官方天气预警 2025年8月1日","request_id":"geniesession-1754039541230-8451:1754039541252-1103:9fxpi","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:12:56.890 INFO log_util.__enter__ 9a69ae03-0fb6-4d35-8db0-38baafea48e0  run start...
2025-08-01 17:12:56.890 INFO log_util.__exit__ 9a69ae03-0fb6-4d35-8db0-38baafea48e0  run cost=[0 ms]
2025-08-01 17:12:56.891 INFO deepsearch.run geniesession-1754039541230-8451:1754039541252-1103:9fxpi 第 1 轮深度搜索...
2025-08-01 17:12:56.891 INFO log_util.__aenter__ 9a69ae03-0fb6-4d35-8db0-38baafea48e0  query_decompose start...
2025-08-01 17:12:56.892 ERROR log_util.__aexit__ 9a69ae03-0fb6-4d35-8db0-38baafea48e0  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:12:56.893 INFO log_util.__aexit__ 9a69ae03-0fb6-4d35-8db0-38baafea48e0 POST /v1/tool/deepsearch cost=[5 ms]
2025-08-01 17:18:14.745 INFO log_util.__aenter__ 20082dc1-a613-4a64-b768-ca03a430cbab POST /v1/tool/deepsearch start...
2025-08-01 17:18:14.746 INFO middleware_util.custom_route_handler 20082dc1-a613-4a64-b768-ca03a430cbab POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"AI development trends in 2024","request_id":"genietest:test:3z8vr","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:18:14.747 INFO log_util.__enter__ 20082dc1-a613-4a64-b768-ca03a430cbab  run start...
2025-08-01 17:18:14.747 INFO log_util.__exit__ 20082dc1-a613-4a64-b768-ca03a430cbab  run cost=[0 ms]
2025-08-01 17:18:14.748 INFO deepsearch.run genietest:test:3z8vr 第 1 轮深度搜索...
2025-08-01 17:18:14.748 INFO log_util.__aenter__ 20082dc1-a613-4a64-b768-ca03a430cbab  query_decompose start...
2025-08-01 17:18:14.749 ERROR log_util.__aexit__ 20082dc1-a613-4a64-b768-ca03a430cbab  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:18:14.750 INFO log_util.__aexit__ 20082dc1-a613-4a64-b768-ca03a430cbab POST /v1/tool/deepsearch cost=[4 ms]
2025-08-01 17:18:25.999 INFO log_util.__aenter__ 9dd08a0c-25ab-46c5-bde4-894ff3f6c4a1 POST /v1/tool/deepsearch start...
2025-08-01 17:18:26.000 INFO middleware_util.custom_route_handler 9dd08a0c-25ab-46c5-bde4-894ff3f6c4a1 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024年AI细分领域发展趋势","request_id":"genietest:test:5zhpm","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:18:26.000 INFO log_util.__enter__ 9dd08a0c-25ab-46c5-bde4-894ff3f6c4a1  run start...
2025-08-01 17:18:26.001 INFO log_util.__exit__ 9dd08a0c-25ab-46c5-bde4-894ff3f6c4a1  run cost=[0 ms]
2025-08-01 17:18:26.001 INFO deepsearch.run genietest:test:5zhpm 第 1 轮深度搜索...
2025-08-01 17:18:26.001 INFO log_util.__aenter__ 9dd08a0c-25ab-46c5-bde4-894ff3f6c4a1  query_decompose start...
2025-08-01 17:18:26.003 ERROR log_util.__aexit__ 9dd08a0c-25ab-46c5-bde4-894ff3f6c4a1  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:18:26.004 INFO log_util.__aexit__ 9dd08a0c-25ab-46c5-bde4-894ff3f6c4a1 POST /v1/tool/deepsearch cost=[5 ms]
2025-08-01 17:18:33.644 INFO log_util.__aenter__ 5a3e2e3e-cbfd-406d-80ae-6b2cd5468117 POST /v1/tool/deepsearch start...
2025-08-01 17:18:33.645 INFO middleware_util.custom_route_handler 5a3e2e3e-cbfd-406d-80ae-6b2cd5468117 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024年人工智能技术预测与趋势","request_id":"genietest:test:zb4px","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:18:33.645 INFO log_util.__enter__ 5a3e2e3e-cbfd-406d-80ae-6b2cd5468117  run start...
2025-08-01 17:18:33.646 INFO log_util.__exit__ 5a3e2e3e-cbfd-406d-80ae-6b2cd5468117  run cost=[1 ms]
2025-08-01 17:18:33.646 INFO deepsearch.run genietest:test:zb4px 第 1 轮深度搜索...
2025-08-01 17:18:33.646 INFO log_util.__aenter__ 5a3e2e3e-cbfd-406d-80ae-6b2cd5468117  query_decompose start...
2025-08-01 17:18:33.647 ERROR log_util.__aexit__ 5a3e2e3e-cbfd-406d-80ae-6b2cd5468117  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:18:33.648 INFO log_util.__aexit__ 5a3e2e3e-cbfd-406d-80ae-6b2cd5468117 POST /v1/tool/deepsearch cost=[3 ms]
2025-08-01 17:18:41.285 INFO log_util.__aenter__ 20031480-24d1-4eb3-a1be-6bfd3f5514c3 POST /v1/tool/deepsearch start...
2025-08-01 17:18:41.286 INFO middleware_util.custom_route_handler 20031480-24d1-4eb3-a1be-6bfd3f5514c3 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024年AI技术权威预测报告","request_id":"genietest:test:ifncv","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:18:41.286 INFO log_util.__enter__ 20031480-24d1-4eb3-a1be-6bfd3f5514c3  run start...
2025-08-01 17:18:41.287 INFO log_util.__exit__ 20031480-24d1-4eb3-a1be-6bfd3f5514c3  run cost=[0 ms]
2025-08-01 17:18:41.287 INFO deepsearch.run genietest:test:ifncv 第 1 轮深度搜索...
2025-08-01 17:18:41.287 INFO log_util.__aenter__ 20031480-24d1-4eb3-a1be-6bfd3f5514c3  query_decompose start...
2025-08-01 17:18:41.288 ERROR log_util.__aexit__ 20031480-24d1-4eb3-a1be-6bfd3f5514c3  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:18:41.289 INFO log_util.__aexit__ 20031480-24d1-4eb3-a1be-6bfd3f5514c3 POST /v1/tool/deepsearch cost=[3 ms]
2025-08-01 17:18:49.490 INFO log_util.__aenter__ 75c849bb-9486-48a7-9025-1fdc95d44e30 POST /v1/tool/deepsearch start...
2025-08-01 17:18:49.491 INFO middleware_util.custom_route_handler 75c849bb-9486-48a7-9025-1fdc95d44e30 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024年人工智能领域权威机构预测","request_id":"genietest:test:21sts","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:18:49.492 INFO log_util.__enter__ 75c849bb-9486-48a7-9025-1fdc95d44e30  run start...
2025-08-01 17:18:49.492 INFO log_util.__exit__ 75c849bb-9486-48a7-9025-1fdc95d44e30  run cost=[0 ms]
2025-08-01 17:18:49.492 INFO deepsearch.run genietest:test:21sts 第 1 轮深度搜索...
2025-08-01 17:18:49.492 INFO log_util.__aenter__ 75c849bb-9486-48a7-9025-1fdc95d44e30  query_decompose start...
2025-08-01 17:18:49.493 ERROR log_util.__aexit__ 75c849bb-9486-48a7-9025-1fdc95d44e30  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:18:49.494 INFO log_util.__aexit__ 75c849bb-9486-48a7-9025-1fdc95d44e30 POST /v1/tool/deepsearch cost=[3 ms]
2025-08-01 17:19:01.532 INFO log_util.__aenter__ ca734b1c-b5e3-455a-ac92-a76588e6967a POST /v1/tool/report start...
2025-08-01 17:19:01.533 INFO middleware_util.custom_route_handler ca734b1c-b5e3-455a-ac92-a76588e6967a POST /v1/tool/report body={"contentStream":true,"fileDescription":"2024年人工智能发展趋势分析报告","fileName":"2024年AI发展趋势分析.html","fileNames":[],"fileType":"html","query":"Analyze AI development trends in 2024","requestId":"genietest:test","stream":true,"streamMode":{"mode":"token","token":10},"task":"基于当前已知的人工智能技术动态和行业趋势，分析2024年AI可能的发展方向，包括技术突破、应用场景扩展、政策与伦理挑战等内容。"}
2025-08-01 17:19:01.536 INFO log_util.__enter__ ca734b1c-b5e3-455a-ac92-a76588e6967a enter report start...
2025-08-01 17:19:01.536 INFO log_util.__exit__ ca734b1c-b5e3-455a-ac92-a76588e6967a enter report cost=[0 ms]
2025-08-01 17:19:01.536 INFO log_util.__enter__ ca734b1c-b5e3-455a-ac92-a76588e6967a enter html_report start...
2025-08-01 17:19:01.537 INFO log_util.__exit__ ca734b1c-b5e3-455a-ac92-a76588e6967a enter html_report cost=[1 ms]
2025-08-01 17:19:01.537 INFO log_util.__aenter__ ca734b1c-b5e3-455a-ac92-a76588e6967a  download_all_files start...
2025-08-01 17:19:01.537 INFO log_util.__aexit__ ca734b1c-b5e3-455a-ac92-a76588e6967a  download_all_files cost=[0 ms]
2025-08-01 17:19:01.537 INFO log_util.__enter__ ca734b1c-b5e3-455a-ac92-a76588e6967a  truncate_files start...
2025-08-01 17:19:01.537 INFO log_util.__exit__ ca734b1c-b5e3-455a-ac92-a76588e6967a  truncate_files cost=[0 ms]
2025-08-01 17:19:01.538 INFO log_util.__enter__ ca734b1c-b5e3-455a-ac92-a76588e6967a  truncate_files start...
2025-08-01 17:19:01.538 INFO log_util.__exit__ ca734b1c-b5e3-455a-ac92-a76588e6967a  truncate_files cost=[0 ms]
2025-08-01 17:19:01.539 INFO log_util.__aexit__ ca734b1c-b5e3-455a-ac92-a76588e6967a POST /v1/tool/report cost=[6 ms]
2025-08-01 17:19:13.774 INFO log_util.__aenter__ f285f678-9dcc-4569-a073-2a297ebd86fb POST /v1/tool/report start...
2025-08-01 17:19:13.775 INFO middleware_util.custom_route_handler f285f678-9dcc-4569-a073-2a297ebd86fb POST /v1/tool/report body={"contentStream":true,"fileDescription":"2024年人工智能发展趋势分析报告","fileName":"2024年AI发展趋势分析.html","fileNames":[],"fileType":"html","query":"Analyze AI development trends in 2024","requestId":"genietest:test","stream":true,"streamMode":{"mode":"token","token":10},"task":"总结2024年AI可能的发展方向，包括技术突破（如多模态AI、自监督学习）、应用场景扩展（如医疗、金融、自动驾驶）、政策与伦理挑战（如数据隐私、AI监管）等内容。"}
2025-08-01 17:19:13.776 INFO log_util.__enter__ f285f678-9dcc-4569-a073-2a297ebd86fb enter report start...
2025-08-01 17:19:13.776 INFO log_util.__exit__ f285f678-9dcc-4569-a073-2a297ebd86fb enter report cost=[0 ms]
2025-08-01 17:19:13.776 INFO log_util.__enter__ f285f678-9dcc-4569-a073-2a297ebd86fb enter html_report start...
2025-08-01 17:19:13.776 INFO log_util.__exit__ f285f678-9dcc-4569-a073-2a297ebd86fb enter html_report cost=[0 ms]
2025-08-01 17:19:13.777 INFO log_util.__aenter__ f285f678-9dcc-4569-a073-2a297ebd86fb  download_all_files start...
2025-08-01 17:19:13.777 INFO log_util.__aexit__ f285f678-9dcc-4569-a073-2a297ebd86fb  download_all_files cost=[0 ms]
2025-08-01 17:19:13.777 INFO log_util.__enter__ f285f678-9dcc-4569-a073-2a297ebd86fb  truncate_files start...
2025-08-01 17:19:13.777 INFO log_util.__exit__ f285f678-9dcc-4569-a073-2a297ebd86fb  truncate_files cost=[0 ms]
2025-08-01 17:19:13.777 INFO log_util.__enter__ f285f678-9dcc-4569-a073-2a297ebd86fb  truncate_files start...
2025-08-01 17:19:13.777 INFO log_util.__exit__ f285f678-9dcc-4569-a073-2a297ebd86fb  truncate_files cost=[0 ms]
2025-08-01 17:19:13.778 INFO log_util.__aexit__ f285f678-9dcc-4569-a073-2a297ebd86fb POST /v1/tool/report cost=[4 ms]
2025-08-01 17:19:14.079 INFO log_util.__aenter__ 692f9f18-14a3-4e38-8be1-871be26f1d71 POST /v1/tool/deepsearch start...
2025-08-01 17:19:14.080 INFO middleware_util.custom_route_handler 692f9f18-14a3-4e38-8be1-871be26f1d71 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"machine learning trends 2024","request_id":"geniemulti-agent-test:multi-agent-test-001:iflpq","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:19:14.081 INFO log_util.__enter__ 692f9f18-14a3-4e38-8be1-871be26f1d71  run start...
2025-08-01 17:19:14.081 INFO log_util.__exit__ 692f9f18-14a3-4e38-8be1-871be26f1d71  run cost=[0 ms]
2025-08-01 17:19:14.082 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:iflpq 第 1 轮深度搜索...
2025-08-01 17:19:14.082 INFO log_util.__aenter__ 692f9f18-14a3-4e38-8be1-871be26f1d71  query_decompose start...
2025-08-01 17:19:14.083 ERROR log_util.__aexit__ 692f9f18-14a3-4e38-8be1-871be26f1d71  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:19:14.084 INFO log_util.__aexit__ 692f9f18-14a3-4e38-8be1-871be26f1d71 POST /v1/tool/deepsearch cost=[5 ms]
2025-08-01 17:19:24.583 INFO log_util.__aenter__ 68935918-a001-485e-bc17-f336e4b7a846 POST /v1/tool/deepsearch start...
2025-08-01 17:19:24.583 INFO log_util.__aenter__ b49f64cf-edb5-458a-b16c-b9f6a1681675 POST /v1/tool/deepsearch start...
2025-08-01 17:19:24.584 INFO middleware_util.custom_route_handler 68935918-a001-485e-bc17-f336e4b7a846 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024 machine learning technology developments","request_id":"geniemulti-agent-test:multi-agent-test-001:md1ub","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:19:24.585 INFO middleware_util.custom_route_handler b49f64cf-edb5-458a-b16c-b9f6a1681675 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024 AI application trends","request_id":"geniemulti-agent-test:multi-agent-test-001:vssir","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:19:24.585 INFO log_util.__enter__ 68935918-a001-485e-bc17-f336e4b7a846  run start...
2025-08-01 17:19:24.586 INFO log_util.__exit__ 68935918-a001-485e-bc17-f336e4b7a846  run cost=[0 ms]
2025-08-01 17:19:24.586 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:md1ub 第 1 轮深度搜索...
2025-08-01 17:19:24.586 INFO log_util.__aenter__ 68935918-a001-485e-bc17-f336e4b7a846  query_decompose start...
2025-08-01 17:19:24.587 ERROR log_util.__aexit__ 68935918-a001-485e-bc17-f336e4b7a846  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:19:24.588 INFO log_util.__enter__ b49f64cf-edb5-458a-b16c-b9f6a1681675  run start...
2025-08-01 17:19:24.588 INFO log_util.__exit__ b49f64cf-edb5-458a-b16c-b9f6a1681675  run cost=[0 ms]
2025-08-01 17:19:24.588 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:vssir 第 1 轮深度搜索...
2025-08-01 17:19:24.589 INFO log_util.__aenter__ b49f64cf-edb5-458a-b16c-b9f6a1681675  query_decompose start...
2025-08-01 17:19:24.590 ERROR log_util.__aexit__ b49f64cf-edb5-458a-b16c-b9f6a1681675  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:19:24.591 INFO log_util.__aexit__ 68935918-a001-485e-bc17-f336e4b7a846 POST /v1/tool/deepsearch cost=[7 ms]
2025-08-01 17:19:24.592 INFO log_util.__aexit__ b49f64cf-edb5-458a-b16c-b9f6a1681675 POST /v1/tool/deepsearch cost=[8 ms]
2025-08-01 17:19:30.763 INFO log_util.__aenter__ 1bc27145-b956-4669-9ddc-13321ddaa22c POST /v1/file_tool/upload_file start...
2025-08-01 17:19:30.763 INFO middleware_util.custom_route_handler 1bc27145-b956-4669-9ddc-13321ddaa22c POST /v1/file_tool/upload_file body={"content":"### 2024年人工智能发展趋势分析\n\n#### 1. 技术突破\n- **多模态AI**：结合文本、图像、语音等多种数据形式的AI模型将更加成熟。\n- **自监督学习**：减少对标注数据的依赖，提升模型泛化能力。\n- **边缘AI**：AI模型在本地设备上的部署将更加普及。\n\n#### 2. 应用场景扩展\n- **医疗**：AI辅助诊断、药物研发将取得突破。\n- **金融**：智能风控、个性化金融服务将更广泛应用。\n- **自动驾驶**：L4级别自动驾驶技术有望在特定场景落地。\n\n#### 3. 政策与伦理挑战\n- **数据隐私**：各国将加强对AI数据使用的监管。\n- **AI监管**：全球范围内AI伦理框架将逐步完善。\n- **就业影响**：AI对劳动力市场的冲击将引发更多讨论。","description":"2024年人工智能发展趋势分析报告","fileName":"2024年AI发展趋势分析.md","requestId":"genietest:test"}
2025-08-01 17:19:30.765 INFO log_util.__aenter__ 1bc27145-b956-4669-9ddc-13321ddaa22c  add_by_content start...
2025-08-01 17:19:30.766 ERROR log_util.__aexit__ 1bc27145-b956-4669-9ddc-13321ddaa22c  add_by_content error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\genietest:test'

2025-08-01 17:19:30.770 ERROR middleware_util.dispatch 1bc27145-b956-4669-9ddc-13321ddaa22c POST /v1/file_tool/upload_file error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 27, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 47, in custom_route_handler
    return await original_route_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\api\file_manage.py", line 35, in upload_file
    file_info = await FileInfoOp.add_by_content(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\genietest:test'

2025-08-01 17:19:30.772 INFO log_util.__aexit__ 1bc27145-b956-4669-9ddc-13321ddaa22c POST /v1/file_tool/upload_file cost=[9 ms]
2025-08-01 17:19:33.673 INFO log_util.__aenter__ b032586a-1a25-425a-a5db-c803e1eaacab POST /v1/tool/deepsearch start...
2025-08-01 17:19:33.674 INFO log_util.__aenter__ 72611b13-e62a-4806-b7fc-626534565af9 POST /v1/tool/deepsearch start...
2025-08-01 17:19:33.675 INFO middleware_util.custom_route_handler b032586a-1a25-425a-a5db-c803e1eaacab POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024 machine learning research directions","request_id":"geniemulti-agent-test:multi-agent-test-001:d7vsg","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:19:33.675 INFO middleware_util.custom_route_handler 72611b13-e62a-4806-b7fc-626534565af9 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024 AI industry trends","request_id":"geniemulti-agent-test:multi-agent-test-001:26bys","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:19:33.676 INFO log_util.__enter__ b032586a-1a25-425a-a5db-c803e1eaacab  run start...
2025-08-01 17:19:33.676 INFO log_util.__exit__ b032586a-1a25-425a-a5db-c803e1eaacab  run cost=[0 ms]
2025-08-01 17:19:33.676 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:d7vsg 第 1 轮深度搜索...
2025-08-01 17:19:33.676 INFO log_util.__aenter__ b032586a-1a25-425a-a5db-c803e1eaacab  query_decompose start...
2025-08-01 17:19:33.678 ERROR log_util.__aexit__ b032586a-1a25-425a-a5db-c803e1eaacab  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:19:33.678 INFO log_util.__enter__ 72611b13-e62a-4806-b7fc-626534565af9  run start...
2025-08-01 17:19:33.679 INFO log_util.__exit__ 72611b13-e62a-4806-b7fc-626534565af9  run cost=[1 ms]
2025-08-01 17:19:33.679 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:26bys 第 1 轮深度搜索...
2025-08-01 17:19:33.679 INFO log_util.__aenter__ 72611b13-e62a-4806-b7fc-626534565af9  query_decompose start...
2025-08-01 17:19:33.680 ERROR log_util.__aexit__ 72611b13-e62a-4806-b7fc-626534565af9  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:19:33.681 INFO log_util.__aexit__ b032586a-1a25-425a-a5db-c803e1eaacab POST /v1/tool/deepsearch cost=[8 ms]
2025-08-01 17:19:33.682 INFO log_util.__aexit__ 72611b13-e62a-4806-b7fc-626534565af9 POST /v1/tool/deepsearch cost=[7 ms]
2025-08-01 17:19:43.556 INFO log_util.__aenter__ be1eea2e-59f4-49b6-8316-13becf390f36 POST /v1/tool/deepsearch start...
2025-08-01 17:19:43.557 INFO log_util.__aenter__ c94f7377-131c-4050-9c71-f798d67828e6 POST /v1/tool/deepsearch start...
2025-08-01 17:19:43.557 INFO middleware_util.custom_route_handler be1eea2e-59f4-49b6-8316-13becf390f36 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024 machine learning technology predictions","request_id":"geniemulti-agent-test:multi-agent-test-001:5gyaw","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:19:43.559 INFO middleware_util.custom_route_handler c94f7377-131c-4050-9c71-f798d67828e6 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024 artificial intelligence development trends","request_id":"geniemulti-agent-test:multi-agent-test-001:f0x5f","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:19:43.560 INFO log_util.__enter__ be1eea2e-59f4-49b6-8316-13becf390f36  run start...
2025-08-01 17:19:43.560 INFO log_util.__exit__ be1eea2e-59f4-49b6-8316-13becf390f36  run cost=[0 ms]
2025-08-01 17:19:43.561 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:5gyaw 第 1 轮深度搜索...
2025-08-01 17:19:43.561 INFO log_util.__aenter__ be1eea2e-59f4-49b6-8316-13becf390f36  query_decompose start...
2025-08-01 17:19:43.562 ERROR log_util.__aexit__ be1eea2e-59f4-49b6-8316-13becf390f36  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:19:43.563 INFO log_util.__enter__ c94f7377-131c-4050-9c71-f798d67828e6  run start...
2025-08-01 17:19:43.563 INFO log_util.__exit__ c94f7377-131c-4050-9c71-f798d67828e6  run cost=[0 ms]
2025-08-01 17:19:43.563 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:f0x5f 第 1 轮深度搜索...
2025-08-01 17:19:43.564 INFO log_util.__aenter__ c94f7377-131c-4050-9c71-f798d67828e6  query_decompose start...
2025-08-01 17:19:43.565 ERROR log_util.__aexit__ c94f7377-131c-4050-9c71-f798d67828e6  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:19:43.565 INFO log_util.__aexit__ be1eea2e-59f4-49b6-8316-13becf390f36 POST /v1/tool/deepsearch cost=[8 ms]
2025-08-01 17:19:43.566 INFO log_util.__aexit__ c94f7377-131c-4050-9c71-f798d67828e6 POST /v1/tool/deepsearch cost=[8 ms]
2025-08-01 17:19:53.032 INFO log_util.__aenter__ 6b2b4654-bcfe-4e2b-9346-ac6eecfaad65 POST /v1/tool/deepsearch start...
2025-08-01 17:19:53.033 INFO log_util.__aenter__ b3e77496-8373-42e3-b7f1-6ef250bf918d POST /v1/tool/deepsearch start...
2025-08-01 17:19:53.034 INFO middleware_util.custom_route_handler 6b2b4654-bcfe-4e2b-9346-ac6eecfaad65 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024 artificial intelligence development trends","request_id":"geniemulti-agent-test:multi-agent-test-001:k1hgl","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:19:53.034 INFO middleware_util.custom_route_handler b3e77496-8373-42e3-b7f1-6ef250bf918d POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024 machine learning technology predictions","request_id":"geniemulti-agent-test:multi-agent-test-001:xy62b","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:19:53.035 INFO log_util.__enter__ 6b2b4654-bcfe-4e2b-9346-ac6eecfaad65  run start...
2025-08-01 17:19:53.035 INFO log_util.__exit__ 6b2b4654-bcfe-4e2b-9346-ac6eecfaad65  run cost=[0 ms]
2025-08-01 17:19:53.035 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:k1hgl 第 1 轮深度搜索...
2025-08-01 17:19:53.035 INFO log_util.__aenter__ 6b2b4654-bcfe-4e2b-9346-ac6eecfaad65  query_decompose start...
2025-08-01 17:19:53.036 ERROR log_util.__aexit__ 6b2b4654-bcfe-4e2b-9346-ac6eecfaad65  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:19:53.037 INFO log_util.__enter__ b3e77496-8373-42e3-b7f1-6ef250bf918d  run start...
2025-08-01 17:19:53.037 INFO log_util.__exit__ b3e77496-8373-42e3-b7f1-6ef250bf918d  run cost=[0 ms]
2025-08-01 17:19:53.037 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:xy62b 第 1 轮深度搜索...
2025-08-01 17:19:53.038 INFO log_util.__aenter__ b3e77496-8373-42e3-b7f1-6ef250bf918d  query_decompose start...
2025-08-01 17:19:53.039 ERROR log_util.__aexit__ b3e77496-8373-42e3-b7f1-6ef250bf918d  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:19:53.040 INFO log_util.__aexit__ 6b2b4654-bcfe-4e2b-9346-ac6eecfaad65 POST /v1/tool/deepsearch cost=[8 ms]
2025-08-01 17:19:53.041 INFO log_util.__aexit__ b3e77496-8373-42e3-b7f1-6ef250bf918d POST /v1/tool/deepsearch cost=[9 ms]
2025-08-01 17:19:57.294 INFO log_util.__aenter__ 05db742e-54e6-4ae2-a88b-61a0ab192ead POST /v1/tool/report start...
2025-08-01 17:19:57.295 INFO middleware_util.custom_route_handler 05db742e-54e6-4ae2-a88b-61a0ab192ead POST /v1/tool/report body={"contentStream":true,"fileDescription":"A simple HTML report about AI","fileName":"AI_report.html","fileNames":[],"fileType":"html","query":"Generate a simple HTML report about AI","requestId":"geniefile-test:file-test-001","stream":true,"streamMode":{"mode":"token","token":10},"task":"Generate a simple HTML report summarizing key aspects of AI, including its definition, applications, and future trends."}
2025-08-01 17:19:57.295 INFO log_util.__enter__ 05db742e-54e6-4ae2-a88b-61a0ab192ead enter report start...
2025-08-01 17:19:57.295 INFO log_util.__exit__ 05db742e-54e6-4ae2-a88b-61a0ab192ead enter report cost=[0 ms]
2025-08-01 17:19:57.296 INFO log_util.__enter__ 05db742e-54e6-4ae2-a88b-61a0ab192ead enter html_report start...
2025-08-01 17:19:57.296 INFO log_util.__exit__ 05db742e-54e6-4ae2-a88b-61a0ab192ead enter html_report cost=[0 ms]
2025-08-01 17:19:57.296 INFO log_util.__aenter__ 05db742e-54e6-4ae2-a88b-61a0ab192ead  download_all_files start...
2025-08-01 17:19:57.296 INFO log_util.__aexit__ 05db742e-54e6-4ae2-a88b-61a0ab192ead  download_all_files cost=[0 ms]
2025-08-01 17:19:57.296 INFO log_util.__enter__ 05db742e-54e6-4ae2-a88b-61a0ab192ead  truncate_files start...
2025-08-01 17:19:57.296 INFO log_util.__exit__ 05db742e-54e6-4ae2-a88b-61a0ab192ead  truncate_files cost=[0 ms]
2025-08-01 17:19:57.297 INFO log_util.__enter__ 05db742e-54e6-4ae2-a88b-61a0ab192ead  truncate_files start...
2025-08-01 17:19:57.297 INFO log_util.__exit__ 05db742e-54e6-4ae2-a88b-61a0ab192ead  truncate_files cost=[0 ms]
2025-08-01 17:19:57.298 INFO log_util.__aexit__ 05db742e-54e6-4ae2-a88b-61a0ab192ead POST /v1/tool/report cost=[4 ms]
2025-08-01 17:20:02.910 INFO log_util.__aenter__ 65a123af-fe9a-4070-b263-1111ec3bc693 POST /v1/tool/deepsearch start...
2025-08-01 17:20:02.910 INFO log_util.__aenter__ 85e045b2-51f4-471d-a219-9ba0785b278f POST /v1/tool/deepsearch start...
2025-08-01 17:20:02.911 INFO middleware_util.custom_route_handler 65a123af-fe9a-4070-b263-1111ec3bc693 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024 AI and machine learning industry predictions","request_id":"geniemulti-agent-test:multi-agent-test-001:ts95b","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:20:02.912 INFO middleware_util.custom_route_handler 85e045b2-51f4-471d-a219-9ba0785b278f POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024 machine learning technology advancements","request_id":"geniemulti-agent-test:multi-agent-test-001:ckifa","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:20:02.912 INFO log_util.__enter__ 65a123af-fe9a-4070-b263-1111ec3bc693  run start...
2025-08-01 17:20:02.912 INFO log_util.__exit__ 65a123af-fe9a-4070-b263-1111ec3bc693  run cost=[0 ms]
2025-08-01 17:20:02.912 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:ts95b 第 1 轮深度搜索...
2025-08-01 17:20:02.913 INFO log_util.__aenter__ 65a123af-fe9a-4070-b263-1111ec3bc693  query_decompose start...
2025-08-01 17:20:02.914 ERROR log_util.__aexit__ 65a123af-fe9a-4070-b263-1111ec3bc693  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:20:02.914 INFO log_util.__enter__ 85e045b2-51f4-471d-a219-9ba0785b278f  run start...
2025-08-01 17:20:02.915 INFO log_util.__exit__ 85e045b2-51f4-471d-a219-9ba0785b278f  run cost=[1 ms]
2025-08-01 17:20:02.915 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:ckifa 第 1 轮深度搜索...
2025-08-01 17:20:02.915 INFO log_util.__aenter__ 85e045b2-51f4-471d-a219-9ba0785b278f  query_decompose start...
2025-08-01 17:20:02.916 ERROR log_util.__aexit__ 85e045b2-51f4-471d-a219-9ba0785b278f  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:20:02.917 INFO log_util.__aexit__ 65a123af-fe9a-4070-b263-1111ec3bc693 POST /v1/tool/deepsearch cost=[6 ms]
2025-08-01 17:20:02.918 INFO log_util.__aexit__ 85e045b2-51f4-471d-a219-9ba0785b278f POST /v1/tool/deepsearch cost=[8 ms]
2025-08-01 17:20:08.352 INFO log_util.__aenter__ 8c98f9b4-111b-4547-bdcd-4bfd72b82ab0 POST /v1/tool/deepsearch start...
2025-08-01 17:20:08.352 INFO middleware_util.custom_route_handler 8c98f9b4-111b-4547-bdcd-4bfd72b82ab0 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"AI的定义、应用和未来趋势","request_id":"geniefile-test:file-test-001:6zxt3","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:20:08.353 INFO log_util.__enter__ 8c98f9b4-111b-4547-bdcd-4bfd72b82ab0  run start...
2025-08-01 17:20:08.354 INFO log_util.__exit__ 8c98f9b4-111b-4547-bdcd-4bfd72b82ab0  run cost=[0 ms]
2025-08-01 17:20:08.354 INFO deepsearch.run geniefile-test:file-test-001:6zxt3 第 1 轮深度搜索...
2025-08-01 17:20:08.354 INFO log_util.__aenter__ 8c98f9b4-111b-4547-bdcd-4bfd72b82ab0  query_decompose start...
2025-08-01 17:20:08.355 ERROR log_util.__aexit__ 8c98f9b4-111b-4547-bdcd-4bfd72b82ab0  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:20:08.356 INFO log_util.__aexit__ 8c98f9b4-111b-4547-bdcd-4bfd72b82ab0 POST /v1/tool/deepsearch cost=[3 ms]
2025-08-01 17:20:11.796 INFO log_util.__aenter__ f5841222-fd9e-4d7c-9a51-ae23c979efb1 POST /v1/tool/deepsearch start...
2025-08-01 17:20:11.796 INFO log_util.__aenter__ 92de9d1a-1600-420d-8d20-4007d2e5e580 POST /v1/tool/deepsearch start...
2025-08-01 17:20:11.797 INFO middleware_util.custom_route_handler f5841222-fd9e-4d7c-9a51-ae23c979efb1 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024 artificial intelligence trends and predictions","request_id":"geniemulti-agent-test:multi-agent-test-001:yj84u","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:20:11.798 INFO middleware_util.custom_route_handler 92de9d1a-1600-420d-8d20-4007d2e5e580 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024 machine learning technology outlook","request_id":"geniemulti-agent-test:multi-agent-test-001:estce","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:20:11.799 INFO log_util.__enter__ f5841222-fd9e-4d7c-9a51-ae23c979efb1  run start...
2025-08-01 17:20:11.799 INFO log_util.__exit__ f5841222-fd9e-4d7c-9a51-ae23c979efb1  run cost=[0 ms]
2025-08-01 17:20:11.799 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:yj84u 第 1 轮深度搜索...
2025-08-01 17:20:11.799 INFO log_util.__aenter__ f5841222-fd9e-4d7c-9a51-ae23c979efb1  query_decompose start...
2025-08-01 17:20:11.801 ERROR log_util.__aexit__ f5841222-fd9e-4d7c-9a51-ae23c979efb1  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:20:11.801 INFO log_util.__enter__ 92de9d1a-1600-420d-8d20-4007d2e5e580  run start...
2025-08-01 17:20:11.801 INFO log_util.__exit__ 92de9d1a-1600-420d-8d20-4007d2e5e580  run cost=[0 ms]
2025-08-01 17:20:11.802 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:estce 第 1 轮深度搜索...
2025-08-01 17:20:11.802 INFO log_util.__aenter__ 92de9d1a-1600-420d-8d20-4007d2e5e580  query_decompose start...
2025-08-01 17:20:11.803 ERROR log_util.__aexit__ 92de9d1a-1600-420d-8d20-4007d2e5e580  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:20:11.804 INFO log_util.__aexit__ f5841222-fd9e-4d7c-9a51-ae23c979efb1 POST /v1/tool/deepsearch cost=[7 ms]
2025-08-01 17:20:11.804 INFO log_util.__aexit__ 92de9d1a-1600-420d-8d20-4007d2e5e580 POST /v1/tool/deepsearch cost=[7 ms]
2025-08-01 17:20:16.182 INFO log_util.__aenter__ 70f35d82-dc97-49b8-a1a7-e5138680c331 POST /v1/tool/deepsearch start...
2025-08-01 17:20:16.182 INFO middleware_util.custom_route_handler 70f35d82-dc97-49b8-a1a7-e5138680c331 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"人工智能的定义、应用领域和未来发展趋势","request_id":"geniefile-test:file-test-001:kdiwr","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:20:16.183 INFO log_util.__enter__ 70f35d82-dc97-49b8-a1a7-e5138680c331  run start...
2025-08-01 17:20:16.183 INFO log_util.__exit__ 70f35d82-dc97-49b8-a1a7-e5138680c331  run cost=[0 ms]
2025-08-01 17:20:16.183 INFO deepsearch.run geniefile-test:file-test-001:kdiwr 第 1 轮深度搜索...
2025-08-01 17:20:16.184 INFO log_util.__aenter__ 70f35d82-dc97-49b8-a1a7-e5138680c331  query_decompose start...
2025-08-01 17:20:16.185 ERROR log_util.__aexit__ 70f35d82-dc97-49b8-a1a7-e5138680c331  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:20:16.185 INFO log_util.__aexit__ 70f35d82-dc97-49b8-a1a7-e5138680c331 POST /v1/tool/deepsearch cost=[2 ms]
2025-08-01 17:20:21.551 INFO log_util.__aenter__ e99b11bc-1aa8-4989-aeb2-8fe4d22c578e POST /v1/tool/deepsearch start...
2025-08-01 17:20:21.551 INFO log_util.__aenter__ 8ce7ac84-98be-4542-bde7-556299047047 POST /v1/tool/deepsearch start...
2025-08-01 17:20:21.552 INFO middleware_util.custom_route_handler e99b11bc-1aa8-4989-aeb2-8fe4d22c578e POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024年人工智能应用趋势","request_id":"geniemulti-agent-test:multi-agent-test-001:su5zp","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:20:21.553 INFO middleware_util.custom_route_handler 8ce7ac84-98be-4542-bde7-556299047047 POST /v1/tool/deepsearch body={"agent_id":"1","content_stream":true,"query":"2024年机器学习技术发展预测","request_id":"geniemulti-agent-test:multi-agent-test-001:e8dvq","scene_type":"auto_agent","src_configs":{"bing":{"count":5}},"stream":true}
2025-08-01 17:20:21.553 INFO log_util.__enter__ e99b11bc-1aa8-4989-aeb2-8fe4d22c578e  run start...
2025-08-01 17:20:21.554 INFO log_util.__exit__ e99b11bc-1aa8-4989-aeb2-8fe4d22c578e  run cost=[1 ms]
2025-08-01 17:20:21.554 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:su5zp 第 1 轮深度搜索...
2025-08-01 17:20:21.554 INFO log_util.__aenter__ e99b11bc-1aa8-4989-aeb2-8fe4d22c578e  query_decompose start...
2025-08-01 17:20:21.555 ERROR log_util.__aexit__ e99b11bc-1aa8-4989-aeb2-8fe4d22c578e  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:20:21.556 INFO log_util.__enter__ 8ce7ac84-98be-4542-bde7-556299047047  run start...
2025-08-01 17:20:21.556 INFO log_util.__exit__ 8ce7ac84-98be-4542-bde7-556299047047  run cost=[0 ms]
2025-08-01 17:20:21.556 INFO deepsearch.run geniemulti-agent-test:multi-agent-test-001:e8dvq 第 1 轮深度搜索...
2025-08-01 17:20:21.556 INFO log_util.__aenter__ 8ce7ac84-98be-4542-bde7-556299047047  query_decompose start...
2025-08-01 17:20:21.558 ERROR log_util.__aexit__ 8ce7ac84-98be-4542-bde7-556299047047  query_decompose error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\tool\search_component\query_process.py", line 28, in query_decompose
    decompose_prompt = get_prompt("deepsearch")
                       ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\prompt_util.py", line 14, in get_prompt
    return yaml.safe_load(importlib.resources.files("genie_tool.prompt").joinpath(f"{prompt_file}.yaml").read_text())
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\pathlib.py", line 1059, in read_text
    return f.read()
           ^^^^^^^^
UnicodeDecodeError: 'gbk' codec can't decode byte 0xac in position 30: illegal multibyte sequence

2025-08-01 17:20:21.558 INFO log_util.__aexit__ e99b11bc-1aa8-4989-aeb2-8fe4d22c578e POST /v1/tool/deepsearch cost=[6 ms]
2025-08-01 17:20:21.559 INFO log_util.__aexit__ 8ce7ac84-98be-4542-bde7-556299047047 POST /v1/tool/deepsearch cost=[7 ms]
2025-08-01 17:20:25.846 INFO log_util.__aenter__ b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4 POST /v1/tool/report start...
2025-08-01 17:20:25.847 INFO middleware_util.custom_route_handler b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4 POST /v1/tool/report body={"contentStream":true,"fileDescription":"A simple HTML report about AI","fileName":"AI_report.html","fileNames":[],"fileType":"html","query":"Generate a simple HTML report about AI","requestId":"geniefile-test:file-test-001","stream":true,"streamMode":{"mode":"token","token":10},"task":"Generate a simple HTML report summarizing key aspects of AI, including its definition, applications, and future trends."}
2025-08-01 17:20:25.848 INFO log_util.__enter__ b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4 enter report start...
2025-08-01 17:20:25.848 INFO log_util.__exit__ b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4 enter report cost=[0 ms]
2025-08-01 17:20:25.848 INFO log_util.__enter__ b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4 enter html_report start...
2025-08-01 17:20:25.849 INFO log_util.__exit__ b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4 enter html_report cost=[1 ms]
2025-08-01 17:20:25.849 INFO log_util.__aenter__ b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4  download_all_files start...
2025-08-01 17:20:25.849 INFO log_util.__aexit__ b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4  download_all_files cost=[0 ms]
2025-08-01 17:20:25.849 INFO log_util.__enter__ b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4  truncate_files start...
2025-08-01 17:20:25.849 INFO log_util.__exit__ b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4  truncate_files cost=[0 ms]
2025-08-01 17:20:25.850 INFO log_util.__enter__ b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4  truncate_files start...
2025-08-01 17:20:25.850 INFO log_util.__exit__ b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4  truncate_files cost=[0 ms]
2025-08-01 17:20:25.851 INFO log_util.__aexit__ b91fd7f3-e7cc-46a7-b3c9-11d6be3cfed4 POST /v1/tool/report cost=[5 ms]
2025-08-01 17:20:37.885 INFO log_util.__aenter__ 0a538fdd-d5a6-4e61-be4a-d371c2e40b56 POST /v1/tool/report start...
2025-08-01 17:20:37.885 INFO log_util.__aenter__ 9e8cea4a-c2a7-4830-8697-b39736a3caae POST /v1/tool/report start...
2025-08-01 17:20:37.886 INFO middleware_util.custom_route_handler 0a538fdd-d5a6-4e61-be4a-d371c2e40b56 POST /v1/tool/report body={"contentStream":true,"fileDescription":"2024年机器学习趋势综合分析报告","fileName":"2024_machine_learning_trends.ppt","fileNames":[],"fileType":"ppt","query":"Create a comprehensive report about machine learning trends in 2024, include web search results, data analysis, and generate both HTML and PPT formats","requestId":"geniemulti-agent-test:multi-agent-test-001","stream":true,"streamMode":{"mode":"token","token":10},"task":"生成一份关于2024年机器学习趋势的综合报告PPT，包括技术发展、应用领域、行业影响等内容，并基于现有知识和搜索失败的结果进行总结。"}
2025-08-01 17:20:37.887 INFO middleware_util.custom_route_handler 9e8cea4a-c2a7-4830-8697-b39736a3caae POST /v1/tool/report body={"contentStream":true,"fileDescription":"2024年机器学习趋势综合分析报告","fileName":"2024_machine_learning_trends.html","fileNames":[],"fileType":"html","query":"Create a comprehensive report about machine learning trends in 2024, include web search results, data analysis, and generate both HTML and PPT formats","requestId":"geniemulti-agent-test:multi-agent-test-001","stream":true,"streamMode":{"mode":"token","token":10},"task":"生成一份关于2024年机器学习趋势的综合报告，包括技术发展、应用领域、行业影响等内容，并基于现有知识和搜索失败的结果进行总结。"}
2025-08-01 17:20:37.888 INFO log_util.__enter__ 0a538fdd-d5a6-4e61-be4a-d371c2e40b56 enter report start...
2025-08-01 17:20:37.888 INFO log_util.__exit__ 0a538fdd-d5a6-4e61-be4a-d371c2e40b56 enter report cost=[0 ms]
2025-08-01 17:20:37.888 INFO log_util.__enter__ 0a538fdd-d5a6-4e61-be4a-d371c2e40b56 enter ppt_report start...
2025-08-01 17:20:37.889 INFO log_util.__exit__ 0a538fdd-d5a6-4e61-be4a-d371c2e40b56 enter ppt_report cost=[0 ms]
2025-08-01 17:20:37.889 INFO log_util.__aenter__ 0a538fdd-d5a6-4e61-be4a-d371c2e40b56  download_all_files start...
2025-08-01 17:20:37.889 INFO log_util.__aexit__ 0a538fdd-d5a6-4e61-be4a-d371c2e40b56  download_all_files cost=[0 ms]
2025-08-01 17:20:37.889 INFO log_util.__enter__ 0a538fdd-d5a6-4e61-be4a-d371c2e40b56  truncate_files start...
2025-08-01 17:20:37.889 INFO log_util.__exit__ 0a538fdd-d5a6-4e61-be4a-d371c2e40b56  truncate_files cost=[0 ms]
2025-08-01 17:20:37.890 INFO log_util.__enter__ 9e8cea4a-c2a7-4830-8697-b39736a3caae enter report start...
2025-08-01 17:20:37.890 INFO log_util.__exit__ 9e8cea4a-c2a7-4830-8697-b39736a3caae enter report cost=[0 ms]
2025-08-01 17:20:37.890 INFO log_util.__enter__ 9e8cea4a-c2a7-4830-8697-b39736a3caae enter html_report start...
2025-08-01 17:20:37.891 INFO log_util.__exit__ 9e8cea4a-c2a7-4830-8697-b39736a3caae enter html_report cost=[1 ms]
2025-08-01 17:20:37.891 INFO log_util.__aenter__ 9e8cea4a-c2a7-4830-8697-b39736a3caae  download_all_files start...
2025-08-01 17:20:37.891 INFO log_util.__aexit__ 9e8cea4a-c2a7-4830-8697-b39736a3caae  download_all_files cost=[0 ms]
2025-08-01 17:20:37.891 INFO log_util.__enter__ 9e8cea4a-c2a7-4830-8697-b39736a3caae  truncate_files start...
2025-08-01 17:20:37.891 INFO log_util.__exit__ 9e8cea4a-c2a7-4830-8697-b39736a3caae  truncate_files cost=[0 ms]
2025-08-01 17:20:37.891 INFO log_util.__enter__ 9e8cea4a-c2a7-4830-8697-b39736a3caae  truncate_files start...
2025-08-01 17:20:37.891 INFO log_util.__exit__ 9e8cea4a-c2a7-4830-8697-b39736a3caae  truncate_files cost=[0 ms]
2025-08-01 17:20:37.892 INFO log_util.__aexit__ 0a538fdd-d5a6-4e61-be4a-d371c2e40b56 POST /v1/tool/report cost=[6 ms]
2025-08-01 17:20:37.893 INFO log_util.__aexit__ 9e8cea4a-c2a7-4830-8697-b39736a3caae POST /v1/tool/report cost=[7 ms]
2025-08-01 17:20:46.262 INFO log_util.__aenter__ beb4bbb7-2896-45cf-8b08-185ec893a6ea POST /v1/file_tool/upload_file start...
2025-08-01 17:20:46.263 INFO middleware_util.custom_route_handler beb4bbb7-2896-45cf-8b08-185ec893a6ea POST /v1/file_tool/upload_file body={"content":"<!DOCTYPE html>\n<html>\n<head>\n    <title>AI Report</title>\n</head>\n<body>\n    <h1>Artificial Intelligence (AI)</h1>\n    <h2>Definition</h2>\n    <p>AI refers to the simulation of human intelligence in machines that are programmed to think and learn like humans.</p>\n    <h2>Applications</h2>\n    <ul>\n        <li>Healthcare: Diagnosis and treatment planning</li>\n        <li>Finance: Fraud detection and algorithmic trading</li>\n        <li>Transportation: Autonomous vehicles</li>\n        <li>Customer Service: Chatbots and virtual assistants</li>\n    </ul>\n    <h2>Future Trends</h2>\n    <ul>\n        <li>Advancements in natural language processing</li>\n        <li>Increased use of AI in robotics</li>\n        <li>Ethical AI and regulation</li>\n    </ul>\n</body>\n</html>","description":"A simple HTML report about AI","fileName":"AI_report.html","requestId":"geniefile-test:file-test-001"}
2025-08-01 17:20:46.264 INFO log_util.__aenter__ beb4bbb7-2896-45cf-8b08-185ec893a6ea  add_by_content start...
2025-08-01 17:20:46.266 ERROR log_util.__aexit__ beb4bbb7-2896-45cf-8b08-185ec893a6ea  add_by_content error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniefile-test:file-test-001'

2025-08-01 17:20:46.269 ERROR middleware_util.dispatch beb4bbb7-2896-45cf-8b08-185ec893a6ea POST /v1/file_tool/upload_file error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 27, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 47, in custom_route_handler
    return await original_route_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\api\file_manage.py", line 35, in upload_file
    file_info = await FileInfoOp.add_by_content(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniefile-test:file-test-001'

2025-08-01 17:20:46.271 INFO log_util.__aexit__ beb4bbb7-2896-45cf-8b08-185ec893a6ea POST /v1/file_tool/upload_file cost=[8 ms]
2025-08-01 17:20:58.745 INFO log_util.__aenter__ e3d4523b-627c-47dd-9184-460006ee5d3c POST /v1/file_tool/upload_file start...
2025-08-01 17:20:58.746 INFO log_util.__aenter__ 45f11072-876a-4374-8997-529d1f652462 POST /v1/file_tool/upload_file start...
2025-08-01 17:20:58.746 INFO middleware_util.custom_route_handler e3d4523b-627c-47dd-9184-460006ee5d3c POST /v1/file_tool/upload_file body={"content":"<html><head><title>2024年机器学习趋势报告</title></head><body><h1>2024年机器学习趋势</h1><p>1. 自动化机器学习（AutoML）的普及</p><p>2. 联邦学习的广泛应用</p><p>3. 可解释AI的需求增长</p><p>4. 边缘计算与机器学习的结合</p><p>5. 多模态学习的发展</p></body></html>","description":"2024年机器学习趋势综合分析报告","fileName":"2024年机器学习趋势报告.html","requestId":"geniemulti-agent-test:multi-agent-test-001"}
2025-08-01 17:20:58.747 INFO log_util.__aenter__ e3d4523b-627c-47dd-9184-460006ee5d3c  add_by_content start...
2025-08-01 17:20:58.748 ERROR log_util.__aexit__ e3d4523b-627c-47dd-9184-460006ee5d3c  add_by_content error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniemulti-agent-test:multi-agent-test-001'

2025-08-01 17:20:58.750 ERROR middleware_util.dispatch e3d4523b-627c-47dd-9184-460006ee5d3c POST /v1/file_tool/upload_file error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 27, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 47, in custom_route_handler
    return await original_route_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\api\file_manage.py", line 35, in upload_file
    file_info = await FileInfoOp.add_by_content(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniemulti-agent-test:multi-agent-test-001'

2025-08-01 17:20:58.752 INFO log_util.__aexit__ e3d4523b-627c-47dd-9184-460006ee5d3c POST /v1/file_tool/upload_file cost=[7 ms]
2025-08-01 17:20:58.752 INFO middleware_util.custom_route_handler 45f11072-876a-4374-8997-529d1f652462 POST /v1/file_tool/upload_file body={"content":"幻灯片1：标题页\n2024年机器学习趋势\n幻灯片2：自动化机器学习（AutoML）\n幻灯片3：联邦学习\n幻灯片4：可解释AI\n幻灯片5：边缘计算与机器学习\n幻灯片6：多模态学习","description":"2024年机器学习趋势综合分析报告PPT","fileName":"2024年机器学习趋势报告.ppt","requestId":"geniemulti-agent-test:multi-agent-test-001"}
2025-08-01 17:20:58.753 INFO log_util.__aenter__ 45f11072-876a-4374-8997-529d1f652462  add_by_content start...
2025-08-01 17:20:58.754 ERROR log_util.__aexit__ 45f11072-876a-4374-8997-529d1f652462  add_by_content error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniemulti-agent-test:multi-agent-test-001'

2025-08-01 17:20:58.756 ERROR middleware_util.dispatch 45f11072-876a-4374-8997-529d1f652462 POST /v1/file_tool/upload_file error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 27, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 47, in custom_route_handler
    return await original_route_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\api\file_manage.py", line 35, in upload_file
    file_info = await FileInfoOp.add_by_content(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniemulti-agent-test:multi-agent-test-001'

2025-08-01 17:20:58.758 INFO log_util.__aexit__ 45f11072-876a-4374-8997-529d1f652462 POST /v1/file_tool/upload_file cost=[12 ms]
2025-08-01 17:21:01.232 INFO log_util.__aenter__ 93bf76fc-30f4-43a2-b310-09dc50b294ae POST /v1/file_tool/upload_file start...
2025-08-01 17:21:01.233 INFO middleware_util.custom_route_handler 93bf76fc-30f4-43a2-b310-09dc50b294ae POST /v1/file_tool/upload_file body={"content":"# Artificial Intelligence (AI)\n\n## Definition\nAI refers to the simulation of human intelligence in machines that are programmed to think and learn like humans.\n\n## Applications\n- **Healthcare**: Diagnosis and treatment planning\n- **Finance**: Fraud detection and algorithmic trading\n- **Transportation**: Autonomous vehicles\n- **Customer Service**: Chatbots and virtual assistants\n\n## Future Trends\n- Advancements in natural language processing\n- Increased use of AI in robotics\n- Ethical AI and regulation","description":"A simple Markdown report about AI","fileName":"AI_report.md","requestId":"geniefile-test:file-test-001"}
2025-08-01 17:21:01.234 INFO log_util.__aenter__ 93bf76fc-30f4-43a2-b310-09dc50b294ae  add_by_content start...
2025-08-01 17:21:01.235 ERROR log_util.__aexit__ 93bf76fc-30f4-43a2-b310-09dc50b294ae  add_by_content error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniefile-test:file-test-001'

2025-08-01 17:21:01.237 ERROR middleware_util.dispatch 93bf76fc-30f4-43a2-b310-09dc50b294ae POST /v1/file_tool/upload_file error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 27, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 47, in custom_route_handler
    return await original_route_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\api\file_manage.py", line 35, in upload_file
    file_info = await FileInfoOp.add_by_content(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniefile-test:file-test-001'

2025-08-01 17:21:01.239 INFO log_util.__aexit__ 93bf76fc-30f4-43a2-b310-09dc50b294ae POST /v1/file_tool/upload_file cost=[7 ms]
2025-08-01 17:21:14.977 INFO log_util.__aenter__ 35ddf7f9-7705-4cf4-949c-7159da65fc9c POST /v1/file_tool/upload_file start...
2025-08-01 17:21:14.977 INFO middleware_util.custom_route_handler 35ddf7f9-7705-4cf4-949c-7159da65fc9c POST /v1/file_tool/upload_file body={"content":"# Artificial Intelligence (AI)\n\n## Definition\nAI refers to the simulation of human intelligence in machines that are programmed to think and learn like humans.\n\n## Applications\n- **Healthcare**: Diagnosis and treatment planning\n- **Finance**: Fraud detection and algorithmic trading\n- **Transportation**: Autonomous vehicles\n- **Customer Service**: Chatbots and virtual assistants\n\n## Future Trends\n- Advancements in natural language processing\n- Increased use of AI in robotics\n- Ethical AI and regulation","description":"A simple Markdown report about AI","fileName":"AI_report.md","requestId":"geniefile-test:file-test-001"}
2025-08-01 17:21:14.978 INFO log_util.__aenter__ 35ddf7f9-7705-4cf4-949c-7159da65fc9c  add_by_content start...
2025-08-01 17:21:14.979 ERROR log_util.__aexit__ 35ddf7f9-7705-4cf4-949c-7159da65fc9c  add_by_content error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniefile-test:file-test-001'

2025-08-01 17:21:14.981 ERROR middleware_util.dispatch 35ddf7f9-7705-4cf4-949c-7159da65fc9c POST /v1/file_tool/upload_file error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 27, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 47, in custom_route_handler
    return await original_route_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\api\file_manage.py", line 35, in upload_file
    file_info = await FileInfoOp.add_by_content(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniefile-test:file-test-001'

2025-08-01 17:21:14.983 INFO log_util.__aexit__ 35ddf7f9-7705-4cf4-949c-7159da65fc9c POST /v1/file_tool/upload_file cost=[6 ms]
2025-08-01 17:21:19.976 INFO log_util.__aenter__ 02b23114-e5c0-4814-b567-92a7300fb03d POST /v1/file_tool/upload_file start...
2025-08-01 17:21:19.978 INFO log_util.__aenter__ 1daceb2d-4697-4a42-a012-eaa748760fd4 POST /v1/file_tool/upload_file start...
2025-08-01 17:21:19.978 INFO middleware_util.custom_route_handler 02b23114-e5c0-4814-b567-92a7300fb03d POST /v1/file_tool/upload_file body={"content":"Slide 1: Title - 2024 Machine Learning Trends\nSlide 2: AutoML\nSlide 3: Federated Learning\nSlide 4: Explainable AI\nSlide 5: Edge AI\nSlide 6: Multimodal Learning","description":"2024年机器学习趋势PPT报告","fileName":"2024_machine_learning_trends.ppt","requestId":"geniemulti-agent-test:multi-agent-test-001"}
2025-08-01 17:21:19.979 INFO log_util.__aenter__ 02b23114-e5c0-4814-b567-92a7300fb03d  add_by_content start...
2025-08-01 17:21:19.980 ERROR log_util.__aexit__ 02b23114-e5c0-4814-b567-92a7300fb03d  add_by_content error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniemulti-agent-test:multi-agent-test-001'

2025-08-01 17:21:19.981 ERROR middleware_util.dispatch 02b23114-e5c0-4814-b567-92a7300fb03d POST /v1/file_tool/upload_file error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 27, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 47, in custom_route_handler
    return await original_route_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\api\file_manage.py", line 35, in upload_file
    file_info = await FileInfoOp.add_by_content(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniemulti-agent-test:multi-agent-test-001'

2025-08-01 17:21:19.983 INFO middleware_util.custom_route_handler 1daceb2d-4697-4a42-a012-eaa748760fd4 POST /v1/file_tool/upload_file body={"content":"<html><head><title>2024 Machine Learning Trends</title></head><body><h1>2024 Machine Learning Trends</h1><ul><li>AutoML and Democratization of AI</li><li>Federated Learning for Privacy</li><li>Explainable AI (XAI)</li><li>Edge AI and TinyML</li><li>Multimodal Learning</li></ul></body></html>","description":"2024年机器学习趋势HTML报告","fileName":"2024_machine_learning_trends.html","requestId":"geniemulti-agent-test:multi-agent-test-001"}
2025-08-01 17:21:19.984 INFO log_util.__aenter__ 1daceb2d-4697-4a42-a012-eaa748760fd4  add_by_content start...
2025-08-01 17:21:19.984 ERROR log_util.__aexit__ 1daceb2d-4697-4a42-a012-eaa748760fd4  add_by_content error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniemulti-agent-test:multi-agent-test-001'

2025-08-01 17:21:19.985 INFO log_util.__aexit__ 02b23114-e5c0-4814-b567-92a7300fb03d POST /v1/file_tool/upload_file cost=[9 ms]
2025-08-01 17:21:19.987 ERROR middleware_util.dispatch 1daceb2d-4697-4a42-a012-eaa748760fd4 POST /v1/file_tool/upload_file error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 27, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 47, in custom_route_handler
    return await original_route_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\api\file_manage.py", line 35, in upload_file
    file_info = await FileInfoOp.add_by_content(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniemulti-agent-test:multi-agent-test-001'

2025-08-01 17:21:19.989 INFO log_util.__aexit__ 1daceb2d-4697-4a42-a012-eaa748760fd4 POST /v1/file_tool/upload_file cost=[11 ms]
2025-08-01 17:21:37.762 INFO log_util.__aenter__ 62e254ab-f3e5-484d-ba6c-ce12708ea264 POST /v1/file_tool/upload_file start...
2025-08-01 17:21:37.762 INFO middleware_util.custom_route_handler 62e254ab-f3e5-484d-ba6c-ce12708ea264 POST /v1/file_tool/upload_file body={"content":"# 2024 Machine Learning Trends\n\n## Key Trends\n1. **AutoML**: Democratizing AI.\n2. **Federated Learning**: Privacy-focused applications.\n3. **Explainable AI (XAI)**: Regulatory compliance.\n4. **Edge AI**: Real-time processing.\n5. **Multimodal Learning**: Cross-domain data integration.","description":"2024年机器学习趋势Markdown报告","fileName":"2024_machine_learning_trends.md","requestId":"geniemulti-agent-test:multi-agent-test-001"}
2025-08-01 17:21:37.763 INFO log_util.__aenter__ 62e254ab-f3e5-484d-ba6c-ce12708ea264  add_by_content start...
2025-08-01 17:21:37.764 ERROR log_util.__aexit__ 62e254ab-f3e5-484d-ba6c-ce12708ea264  add_by_content error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniemulti-agent-test:multi-agent-test-001'

2025-08-01 17:21:37.766 ERROR middleware_util.dispatch 62e254ab-f3e5-484d-ba6c-ce12708ea264 POST /v1/file_tool/upload_file error=Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 148, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 27, in dispatch
    return await call_next(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\middleware_util.py", line 47, in custom_route_handler
    return await original_route_handler(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\api\file_manage.py", line 35, in upload_file
    file_info = await FileInfoOp.add_by_content(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\util\log_util.py", line 55, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 49, in add_by_content
    file_path = await FileDB.save(filename, content, scope=request_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\JDAGENT\joyagent-jdgenie\genie-tool\genie_tool\db\file_table_op.py", line 26, in save
    os.makedirs(save_path)
  File "<frozen os>", line 225, in makedirs
NotADirectoryError: [WinError 267] 目录名称无效。: 'file_db_dir\\geniemulti-agent-test:multi-agent-test-001'

2025-08-01 17:21:37.768 INFO log_util.__aexit__ 62e254ab-f3e5-484d-ba6c-ce12708ea264 POST /v1/file_tool/upload_file cost=[5 ms]
