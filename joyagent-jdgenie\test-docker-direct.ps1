# Docker直接测试脚本
Write-Host "🐳 Docker直接测试脚本" -ForegroundColor Blue
Write-Host "=================================="

Write-Host "跳过WSL更新，直接测试Docker是否可用" -ForegroundColor Cyan

# 第1步：检查当前WSL状态
Write-Host ""
Write-Host "第1步：检查当前WSL状态..." -ForegroundColor Yellow
try {
    $wslStatus = wsl --status 2>$null
    Write-Host "当前WSL状态："
    Write-Host $wslStatus
    Write-Host "✅ WSL基本功能正常" -ForegroundColor Green
} catch {
    Write-Host "⚠️ WSL状态检查失败，但可能不影响Docker" -ForegroundColor Yellow
}

# 第2步：重启Docker Desktop
Write-Host ""
Write-Host "第2步：重启Docker Desktop..." -ForegroundColor Yellow

try {
    # 停止Docker Desktop
    Write-Host "停止Docker Desktop..."
    Get-Process "Docker Desktop" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 5
    
    # 启动Docker Desktop
    Write-Host "启动Docker Desktop..."
    Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    Write-Host "✅ Docker Desktop已启动" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Desktop启动失败" -ForegroundColor Red
    exit 1
}

# 第3步：等待Docker Engine启动
Write-Host ""
Write-Host "第3步：等待Docker Engine启动..." -ForegroundColor Yellow
Write-Host "这可能需要2-5分钟，请耐心等待..."

$maxAttempts = 60
$attempt = 0
$dockerReady = $false

while ($attempt -lt $maxAttempts -and -not $dockerReady) {
    $attempt++
    $minutes = [math]::Floor($attempt * 5 / 60)
    $seconds = ($attempt * 5) % 60
    Write-Host "检查Docker状态... ($attempt/$maxAttempts) [${minutes}:${seconds:D2}]" -NoNewline
    
    try {
        docker info *>$null 2>&1
        if ($LASTEXITCODE -eq 0) {
            $dockerReady = $true
            Write-Host " ✅" -ForegroundColor Green
            break
        }
    } catch {}
    
    Write-Host " ⏳" -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

if ($dockerReady) {
    Write-Host ""
    Write-Host "✅ Docker Engine已就绪！" -ForegroundColor Green
    
    # 第4步：测试Docker功能
    Write-Host ""
    Write-Host "第4步：测试Docker功能..." -ForegroundColor Yellow
    
    try {
        Write-Host "运行hello-world容器测试..."
        docker run --rm hello-world
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker功能测试成功！" -ForegroundColor Green
            $dockerWorking = $true
        } else {
            Write-Host "❌ Docker功能测试失败" -ForegroundColor Red
            $dockerWorking = $false
        }
    } catch {
        Write-Host "❌ Docker测试异常" -ForegroundColor Red
        $dockerWorking = $false
    }
    
    # 第5步：显示Docker信息
    if ($dockerWorking) {
        Write-Host ""
        Write-Host "第5步：Docker系统信息..." -ForegroundColor Yellow
        try {
            Write-Host "Docker版本信息："
            docker --version
            Write-Host ""
            Write-Host "Docker系统信息："
            docker info | Select-String "Server Version", "Operating System", "Total Memory", "CPUs"
        } catch {
            Write-Host "⚠️ 无法获取详细信息，但Docker基本功能正常" -ForegroundColor Yellow
        }
    }
    
} else {
    Write-Host ""
    Write-Host "❌ Docker启动超时" -ForegroundColor Red
    Write-Host "💡 建议操作：" -ForegroundColor Yellow
    Write-Host "1. 检查Docker Desktop是否正常显示" -ForegroundColor Cyan
    Write-Host "2. 重启计算机后再试" -ForegroundColor Cyan
    Write-Host "3. 或者先更新WSL再试" -ForegroundColor Cyan
    $dockerWorking = $false
}

# 第6步：准备启动JoyAgent-JDGenie
Write-Host ""
Write-Host "=================================="
if ($dockerWorking) {
    Write-Host "🎉 Docker测试成功！可以启动JoyAgent-JDGenie了！" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 下一步操作：" -ForegroundColor Blue
    Write-Host "1. 运行: docker-start.sh" -ForegroundColor Cyan
    Write-Host "2. 或运行: docker-compose up -d --build" -ForegroundColor Cyan
    Write-Host "3. 访问: http://localhost:3000" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📂 项目目录: D:\JDAGENT\joyagent-jdgenie" -ForegroundColor Blue
    
    # 询问是否立即启动
    Write-Host ""
    $choice = Read-Host "是否立即启动JoyAgent-JDGenie？(Y/N)"
    if ($choice -eq "Y" -or $choice -eq "y") {
        Write-Host ""
        Write-Host "🚀 启动JoyAgent-JDGenie..." -ForegroundColor Blue
        try {
            if (Test-Path "docker-start.sh") {
                bash docker-start.sh
            } else {
                docker-compose up -d --build
            }
        } catch {
            Write-Host "❌ 启动失败，请手动运行: docker-start.sh" -ForegroundColor Red
        }
    }
} else {
    Write-Host "❌ Docker未能正常工作" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 建议先解决Docker问题：" -ForegroundColor Yellow
    Write-Host "1. 更新WSL: start ms-windows-store://pdp/?productid=9P9TQF7MRM4R" -ForegroundColor Cyan
    Write-Host "2. 重启计算机" -ForegroundColor Cyan
    Write-Host "3. 检查Docker Desktop设置" -ForegroundColor Cyan
}
Write-Host "=================================="

Read-Host "按回车键退出..."
