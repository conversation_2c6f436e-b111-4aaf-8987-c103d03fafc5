# WSL完整更新脚本
Write-Host "🔄 WSL完整更新脚本" -ForegroundColor Blue
Write-Host "=================================="

# 检查管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "❌ 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按回车键退出..."
    exit 1
}

Write-Host "✅ 管理员权限确认" -ForegroundColor Green

# 第1步：检查当前WSL版本
Write-Host ""
Write-Host "第1步：检查当前WSL版本..." -ForegroundColor Yellow
try {
    $wslStatus = wsl --status 2>$null
    Write-Host "当前WSL状态："
    Write-Host $wslStatus
} catch {
    Write-Host "⚠️ 无法获取WSL状态" -ForegroundColor Yellow
}

# 第2步：停止WSL
Write-Host ""
Write-Host "第2步：停止WSL..." -ForegroundColor Yellow
try {
    wsl --shutdown
    Write-Host "✅ WSL已停止" -ForegroundColor Green
    Start-Sleep -Seconds 3
} catch {
    Write-Host "⚠️ WSL停止可能失败" -ForegroundColor Yellow
}

# 第3步：尝试多种更新方式
Write-Host ""
Write-Host "第3步：尝试更新WSL..." -ForegroundColor Yellow

# 方式1：网络下载更新
Write-Host "方式1：尝试网络下载更新..." -ForegroundColor Cyan
try {
    $process = Start-Process -FilePath "wsl" -ArgumentList "--update", "--web-download" -Wait -PassThru -NoNewWindow
    if ($process.ExitCode -eq 0) {
        Write-Host "✅ 网络更新成功！" -ForegroundColor Green
        $updateSuccess = $true
    } else {
        Write-Host "❌ 网络更新失败，错误代码: $($process.ExitCode)" -ForegroundColor Red
        $updateSuccess = $false
    }
} catch {
    Write-Host "❌ 网络更新异常: $($_.Exception.Message)" -ForegroundColor Red
    $updateSuccess = $false
}

# 方式2：如果网络更新失败，尝试inbox更新
if (-not $updateSuccess) {
    Write-Host ""
    Write-Host "方式2：尝试inbox更新..." -ForegroundColor Cyan
    try {
        $process = Start-Process -FilePath "wsl" -ArgumentList "--update", "--inbox" -Wait -PassThru -NoNewWindow
        if ($process.ExitCode -eq 0) {
            Write-Host "✅ Inbox更新成功！" -ForegroundColor Green
            $updateSuccess = $true
        } else {
            Write-Host "❌ Inbox更新失败，错误代码: $($process.ExitCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Inbox更新异常: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 方式3：如果还是失败，尝试Microsoft Store
if (-not $updateSuccess) {
    Write-Host ""
    Write-Host "方式3：通过Microsoft Store更新..." -ForegroundColor Cyan
    try {
        Start-Process "ms-windows-store://pdp/?productid=9P9TQF7MRM4R"
        Write-Host "✅ Microsoft Store已打开，请手动更新WSL" -ForegroundColor Green
        Write-Host "在Microsoft Store中点击'更新'按钮" -ForegroundColor Yellow
        Read-Host "更新完成后按回车键继续..."
    } catch {
        Write-Host "❌ 无法打开Microsoft Store" -ForegroundColor Red
    }
}

# 方式4：手动下载最新版本
if (-not $updateSuccess) {
    Write-Host ""
    Write-Host "方式4：手动下载最新WSL..." -ForegroundColor Cyan
    try {
        Write-Host "正在下载最新WSL安装包..."
        $wslUrl = "https://github.com/microsoft/WSL/releases/latest/download/Microsoft.WSL_1.0.3.0_x64_ARM64.msixbundle"
        Invoke-WebRequest -Uri $wslUrl -OutFile "WSL_latest.msixbundle"
        
        Write-Host "正在安装WSL更新包..."
        Add-AppxPackage -Path "WSL_latest.msixbundle"
        Write-Host "✅ 手动安装完成！" -ForegroundColor Green
        $updateSuccess = $true
    } catch {
        Write-Host "❌ 手动下载安装失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 第4步：验证更新结果
Write-Host ""
Write-Host "第4步：验证更新结果..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

try {
    $wslStatusAfter = wsl --status 2>$null
    Write-Host "更新后WSL状态："
    Write-Host $wslStatusAfter
    
    if ($wslStatusAfter -match "版本") {
        Write-Host "✅ WSL状态检查成功！" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ 无法验证WSL状态" -ForegroundColor Yellow
}

# 第5步：尝试安装Ubuntu
Write-Host ""
Write-Host "第5步：尝试安装Ubuntu..." -ForegroundColor Yellow

$ubuntuVersions = @("Ubuntu-22.04", "Ubuntu-20.04", "Ubuntu")

foreach ($version in $ubuntuVersions) {
    Write-Host "尝试安装 $version..." -ForegroundColor Cyan
    
    try {
        $process = Start-Process -FilePath "wsl" -ArgumentList "--install", "-d", $version -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Host "✅ $version 安装成功！" -ForegroundColor Green
            $ubuntuInstalled = $true
            break
        } else {
            Write-Host "❌ $version 安装失败，错误代码: $($process.ExitCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $version 安装异常: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Seconds 2
}

# 第6步：启动Docker Desktop
Write-Host ""
Write-Host "第6步：启动Docker Desktop..." -ForegroundColor Yellow

try {
    # 停止Docker Desktop
    Get-Process "Docker Desktop" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 3
    
    # 启动Docker Desktop
    Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    Write-Host "✅ Docker Desktop已启动" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Desktop启动失败" -ForegroundColor Red
}

# 第7步：等待Docker启动
Write-Host ""
Write-Host "第7步：等待Docker启动..." -ForegroundColor Yellow
Write-Host "正在等待Docker Engine启动..."

$maxAttempts = 24
$attempt = 0

while ($attempt -lt $maxAttempts) {
    $attempt++
    Write-Host "检查Docker状态... ($attempt/$maxAttempts)" -NoNewline
    
    try {
        docker info *>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host " ✅ Docker已就绪！" -ForegroundColor Green
            
            # 测试Docker
            Write-Host "测试Docker功能..."
            docker run --rm hello-world *>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Docker测试成功！" -ForegroundColor Green
            }
            break
        }
    } catch {}
    
    Write-Host " ⏳" -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

if ($attempt -eq $maxAttempts) {
    Write-Host "❌ Docker启动超时，请手动检查" -ForegroundColor Red
}

Write-Host ""
Write-Host "=================================="
Write-Host "🎉 WSL和Docker配置完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📂 项目目录: D:\JDAGENT\joyagent-jdgenie" -ForegroundColor Blue
Write-Host "🚀 启动命令: docker-start.sh" -ForegroundColor Blue
Write-Host "🌐 访问地址: http://localhost:3000" -ForegroundColor Blue
Write-Host "=================================="

Read-Host "按回车键继续..."
