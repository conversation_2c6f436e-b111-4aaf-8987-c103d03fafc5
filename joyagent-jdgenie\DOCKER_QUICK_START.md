# 🐳 JoyAgent-JDGenie Docker 快速启动指南

## 📋 前提条件

### 1. 安装Docker Desktop
- **Windows**: 下载并安装 [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop/)
- **macOS**: 下载并安装 [Docker Desktop for Mac](https://www.docker.com/products/docker-desktop/)
- **Linux**: 安装 Docker Engine 和 Docker Compose

### 2. 验证安装
```bash
docker --version
docker-compose --version
```

## 🚀 一键启动

### 方法1：使用启动脚本（推荐）
```bash
# 进入项目目录
cd joyagent-jdgenie

# 运行启动脚本
./docker-start.sh
```

### 方法2：使用Docker Compose命令
```bash
# 进入项目目录
cd joyagent-jdgenie

# 构建并启动服务
docker-compose up -d --build

# 查看启动状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 🌐 访问服务

启动完成后，您可以访问以下地址：

- **🎯 主界面**: http://localhost:3000
- **🔧 后端API**: http://localhost:8080
- **🛠️ 工具服务**: http://localhost:1601
- **🔌 MCP客户端**: http://localhost:8188

## ⚙️ 配置说明

### LLM服务配置
项目已预配置以下LLM服务：

**主要服务 - DeepSeek**:
- API Key: `***********************************`
- Base URL: `https://api.deepseek.com/v1`
- Model: `deepseek-chat`

**备用服务 - 火山方舟**:
- API Key: `bcdd2ded-7352-4668-9fef-ebe31b56177a`
- Base URL: `https://ark.cn-beijing.volces.com/api/v3`

**搜索服务 - Serper**:
- API Key: `04d74f4080b9379d5875e04bccc3000aa8e7840f`

### 修改配置
如需修改配置，请编辑 `docker-compose.yml` 文件中的环境变量部分。

## 🛠️ 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f genie-app

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v

# 重新构建镜像
docker-compose build --no-cache

# 进入容器
docker-compose exec genie-app bash
```

## 🔧 故障排除

### 1. 端口冲突
如果遇到端口冲突，请修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "3001:3000"  # 将前端端口改为3001
  - "8081:8080"  # 将后端端口改为8081
```

### 2. 内存不足
确保Docker Desktop分配足够的内存（建议至少4GB）：
- 打开Docker Desktop
- 进入Settings → Resources → Memory
- 调整内存分配

### 3. 构建失败
```bash
# 清理Docker缓存
docker system prune -a

# 重新构建
docker-compose build --no-cache
```

### 4. 服务无法访问
```bash
# 检查容器状态
docker-compose ps

# 检查容器日志
docker-compose logs genie-app

# 检查网络连接
docker network ls
```

## 📊 性能监控

### 查看资源使用情况
```bash
# 查看容器资源使用
docker stats

# 查看容器详细信息
docker-compose exec genie-app top
```

## 🔄 更新和维护

### 更新代码
```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker-compose up -d --build
```

### 数据备份
```bash
# 备份数据卷
docker run --rm -v joyagent-jdgenie_data:/data -v $(pwd):/backup alpine tar czf /backup/data-backup.tar.gz -C /data .

# 恢复数据
docker run --rm -v joyagent-jdgenie_data:/data -v $(pwd):/backup alpine tar xzf /backup/data-backup.tar.gz -C /data
```

## 🎉 开始使用

1. 确保Docker Desktop正在运行
2. 运行 `./docker-start.sh` 或 `docker-compose up -d --build`
3. 等待服务启动完成（约2-5分钟）
4. 访问 http://localhost:3000 开始使用JoyAgent-JDGenie！

---

**注意**: 首次启动需要下载和构建镜像，可能需要较长时间。后续启动会更快。
