version: '3.8'

services:
  genie-app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    container_name: joyagent-jdgenie-prod
    restart: always
    environment:
      # 生产环境配置
      - NODE_ENV=production
      - JAVA_OPTS=-Xms2g -Xmx4g -XX:+UseG1GC -XX:+UseContainerSupport
      - PYTHONOPTIMIZE=1
      - PYTHONUNBUFFERED=1
      
      # LLM服务配置
      - OPENAI_API_KEY=sk-1d701fa867da47ebb2e1fd8d141795cd
      - OPENAI_BASE_URL=https://api.deepseek.com/v1
      - DEFAULT_MODEL=deepseek/deepseek-chat
      
      # 备用LLM配置
      - DOUBAO_API_KEY=bcdd2ded-7352-4668-9fef-ebe31b56177a
      - DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
      
      # 搜索服务配置
      - SERPER_SEARCH_API_KEY=04d74f4080b9379d5875e04bccc3000aa8e7840f
      - SERPER_SEARCH_URL=https://google.serper.dev/search
      - USE_SEARCH_ENGINE=serp
      
      # 安全配置
      - SENSITIVE_WORD_REPLACE=true
      - CORS_ALLOWED_ORIGINS=https://your-domain.com
      
      # 数据库配置
      - FILE_SAVE_PATH=/app/data/files
      - SQLITE_DB_PATH=/app/data/autobots.db
      - FILE_SERVER_URL=http://127.0.0.1:1601/v1/file_tool
      
      # 日志配置
      - LOG_LEVEL=INFO
      - LOG_FILE=/app/logs/genie.log
    
    ports:
      - "127.0.0.1:3000:3000"  # 仅本地访问，通过Nginx代理
      - "127.0.0.1:8080:8080"
      - "127.0.0.1:1601:1601"
      - "127.0.0.1:8188:8188"
    
    volumes:
      - genie-data:/app/data
      - genie-logs:/app/logs
      - ./config:/app/config:ro
    
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000", "&&", "curl", "-f", "http://localhost:8080/web/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
        compress: "true"
    
    networks:
      - genie-network

  nginx:
    image: nginx:alpine
    container_name: genie-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - genie-app
    networks:
      - genie-network
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  watchtower:
    image: containrrr/watchtower
    container_name: genie-watchtower
    restart: always
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_POLL_INTERVAL=3600
      - WATCHTOWER_INCLUDE_STOPPED=true
    command: --interval 3600 joyagent-jdgenie-prod

volumes:
  genie-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/genie/data
  genie-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/genie/logs

networks:
  genie-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
