# 🐳 JoyAgent-JDGenie Docker 常用命令

## 基本操作

### 启动和停止
```bash
# 启动所有服务
docker-compose up -d

# 启动并重新构建
docker-compose up -d --build

# 停止所有服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v

# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart genie-app
```

### 查看状态
```bash
# 查看容器状态
docker-compose ps

# 查看详细状态
docker-compose ps -a

# 查看资源使用
docker stats

# 查看容器详细信息
docker inspect joyagent-jdgenie
```

## 日志管理

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs

# 实时查看日志
docker-compose logs -f

# 查看最近100行日志
docker-compose logs --tail=100

# 查看特定服务日志
docker-compose logs genie-app

# 查看特定时间段日志
docker-compose logs --since="2024-01-01T00:00:00" --until="2024-01-01T23:59:59"
```

### 日志导出
```bash
# 导出日志到文件
docker-compose logs > genie-logs.txt

# 导出特定服务日志
docker-compose logs genie-app > genie-app-logs.txt
```

## 容器管理

### 进入容器
```bash
# 进入主容器
docker-compose exec genie-app bash

# 以root用户进入
docker-compose exec --user root genie-app bash

# 执行单个命令
docker-compose exec genie-app ls -la
```

### 文件操作
```bash
# 从容器复制文件到主机
docker cp joyagent-jdgenie:/app/logs ./logs

# 从主机复制文件到容器
docker cp ./config.yml joyagent-jdgenie:/app/config.yml
```

## 镜像管理

### 构建和清理
```bash
# 重新构建镜像
docker-compose build --no-cache

# 查看镜像
docker images | grep genie

# 删除未使用的镜像
docker image prune -a

# 清理所有未使用的资源
docker system prune -a --volumes
```

## 网络管理

### 网络操作
```bash
# 查看网络
docker network ls

# 查看网络详情
docker network inspect genie-network

# 测试网络连通性
docker-compose exec genie-app ping google.com
```

## 数据管理

### 数据卷操作
```bash
# 查看数据卷
docker volume ls

# 查看数据卷详情
docker volume inspect joyagent-jdgenie_data

# 备份数据卷
docker run --rm -v joyagent-jdgenie_data:/data -v $(pwd):/backup alpine tar czf /backup/backup.tar.gz -C /data .

# 恢复数据卷
docker run --rm -v joyagent-jdgenie_data:/data -v $(pwd):/backup alpine tar xzf /backup/backup.tar.gz -C /data
```

## 性能监控

### 监控命令
```bash
# 实时监控资源使用
docker stats --no-stream

# 查看容器进程
docker-compose exec genie-app ps aux

# 查看端口使用
docker-compose exec genie-app netstat -tlnp
```

## 故障排除

### 调试命令
```bash
# 检查容器健康状态
docker-compose exec genie-app curl -f http://localhost:3000

# 查看环境变量
docker-compose exec genie-app env

# 检查磁盘使用
docker-compose exec genie-app df -h

# 检查内存使用
docker-compose exec genie-app free -h
```

### 重置和清理
```bash
# 完全重置（删除所有数据）
docker-compose down -v
docker system prune -a --volumes
docker-compose up -d --build

# 仅重置容器（保留数据）
docker-compose down
docker-compose up -d --build
```

## 快捷脚本

### 创建别名
```bash
# 添加到 ~/.bashrc 或 ~/.zshrc
alias genie-start="docker-compose up -d"
alias genie-stop="docker-compose down"
alias genie-logs="docker-compose logs -f"
alias genie-status="docker-compose ps"
alias genie-restart="docker-compose restart"
```

### 一键操作脚本
```bash
# 快速重启
echo "docker-compose down && docker-compose up -d" > quick-restart.sh
chmod +x quick-restart.sh

# 快速查看状态
echo "docker-compose ps && docker stats --no-stream" > quick-status.sh
chmod +x quick-status.sh
```
