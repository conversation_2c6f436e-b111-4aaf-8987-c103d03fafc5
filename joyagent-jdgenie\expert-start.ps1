#Requires -Version 5.1
<#
.SYNOPSIS
JoyAgent-JDGenie 专家级启动脚本

.DESCRIPTION
专业级PowerShell脚本，用于在中国大陆环境下启动JoyAgent-JDGenie多智能体系统
#>

[CmdletBinding()]
param()

# 严格模式和错误处理
Set-StrictMode -Version Latest
$ErrorActionPreference = 'Stop'
$ProgressPreference = 'SilentlyContinue'

# 常量定义
$DOCKER_CONFIG_DIR = Join-Path $env:USERPROFILE '.docker'
$DAEMON_CONFIG_FILE = Join-Path $DOCKER_CONFIG_DIR 'daemon.json'
$DOCKER_DESKTOP_PATH = 'C:\Program Files\Docker\Docker\Docker Desktop.exe'

$REGISTRY_MIRRORS = @(
    'https://docker.m.daocloud.io',
    'https://dockerproxy.com',
    'https://mirror.baidubce.com',
    'https://reg-mirror.qiniu.com'
)

$SERVICES = @(
    @{ Name = '前端服务'; Url = 'http://localhost:3000' },
    @{ Name = '后端服务'; Url = 'http://localhost:8080' },
    @{ Name = '工具服务'; Url = 'http://localhost:1601' },
    @{ Name = 'MCP客户端'; Url = 'http://localhost:8188' }
)

# 辅助函数
function Write-StatusMessage {
    param(
        [Parameter(Mandatory)]
        [string]$Message,
        
        [ValidateSet('Info', 'Success', 'Warning', 'Error')]
        [string]$Type = 'Info'
    )
    
    $colors = @{
        'Info'    = 'Cyan'
        'Success' = 'Green'
        'Warning' = 'Yellow'
        'Error'   = 'Red'
    }
    
    $icons = @{
        'Info'    = '🔄'
        'Success' = '✅'
        'Warning' = '⚠️'
        'Error'   = '❌'
    }
    
    Write-Host "$($icons[$Type]) $Message" -ForegroundColor $colors[$Type]
}

function Test-DockerStatus {
    try {
        $null = docker info 2>$null
        return $LASTEXITCODE -eq 0
    }
    catch {
        return $false
    }
}

function Test-ServiceHealth {
    param([string]$Url)
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 3 -UseBasicParsing -ErrorAction SilentlyContinue
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

function Initialize-DockerConfiguration {
    Write-StatusMessage '配置Docker镜像源...' 'Info'
    
    try {
        # 创建配置目录
        if (-not (Test-Path $DOCKER_CONFIG_DIR)) {
            New-Item -ItemType Directory -Path $DOCKER_CONFIG_DIR -Force | Out-Null
        }
        
        # 生成daemon.json配置
        $daemonConfig = @{
            'registry-mirrors' = $REGISTRY_MIRRORS
            'builder' = @{
                'gc' = @{
                    'defaultKeepStorage' = '20GB'
                    'enabled' = $true
                }
            }
            'max-concurrent-downloads' = 3
            'max-concurrent-uploads' = 5
        }
        
        # 写入配置文件
        $daemonConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $DAEMON_CONFIG_FILE -Encoding UTF8
        
        Write-StatusMessage 'Docker镜像源配置完成' 'Success'
    }
    catch {
        Write-StatusMessage "Docker配置失败: $($_.Exception.Message)" 'Error'
        throw
    }
}

function Restart-DockerService {
    Write-StatusMessage '重启Docker Desktop...' 'Info'
    
    try {
        # 停止Docker Desktop进程
        Get-Process -Name 'Docker Desktop' -ErrorAction SilentlyContinue | Stop-Process -Force
        Start-Sleep -Seconds 5
        
        # 验证Docker Desktop路径
        if (-not (Test-Path $DOCKER_DESKTOP_PATH)) {
            throw "Docker Desktop未找到: $DOCKER_DESKTOP_PATH"
        }
        
        # 启动Docker Desktop
        Start-Process -FilePath $DOCKER_DESKTOP_PATH
        Write-StatusMessage 'Docker Desktop已启动' 'Success'
    }
    catch {
        Write-StatusMessage "Docker重启失败: $($_.Exception.Message)" 'Error'
        throw
    }
}

function Wait-DockerReady {
    Write-StatusMessage '等待Docker Engine启动...' 'Info'
    
    $maxAttempts = 60
    $attempt = 0
    
    do {
        $attempt++
        Write-Progress -Activity 'Docker启动检查' -Status "尝试 $attempt/$maxAttempts" -PercentComplete (($attempt / $maxAttempts) * 100)
        
        if (Test-DockerStatus) {
            Write-Progress -Activity 'Docker启动检查' -Completed
            Write-StatusMessage 'Docker Engine已就绪' 'Success'
            return $true
        }
        
        Start-Sleep -Seconds 5
    } while ($attempt -lt $maxAttempts)
    
    Write-Progress -Activity 'Docker启动检查' -Completed
    Write-StatusMessage 'Docker启动超时' 'Error'
    return $false
}

function Test-DockerMirror {
    Write-StatusMessage '测试Docker镜像源...' 'Info'
    
    try {
        docker pull hello-world 2>$null | Out-Null
        
        if ($LASTEXITCODE -eq 0) {
            Write-StatusMessage 'Docker镜像源测试成功' 'Success'
        }
        else {
            Write-StatusMessage 'Docker镜像源可能较慢，但继续执行' 'Warning'
        }
    }
    catch {
        Write-StatusMessage 'Docker镜像源测试失败，但继续执行' 'Warning'
    }
}

function Start-Application {
    Write-StatusMessage '启动JoyAgent-JDGenie...' 'Info'
    
    try {
        # 验证docker-compose.yml存在
        if (-not (Test-Path 'docker-compose.yml')) {
            throw 'docker-compose.yml文件未找到'
        }
        
        # 启动应用
        docker-compose up -d --build
        
        if ($LASTEXITCODE -eq 0) {
            Write-StatusMessage 'JoyAgent-JDGenie启动成功' 'Success'
        }
        else {
            throw "docker-compose执行失败，退出代码: $LASTEXITCODE"
        }
    }
    catch {
        Write-StatusMessage "应用启动失败: $($_.Exception.Message)" 'Error'
        throw
    }
}

function Wait-ServicesReady {
    Write-StatusMessage '等待服务启动...' 'Info'
    
    $timeout = 300
    $startTime = Get-Date
    $endTime = $startTime.AddSeconds($timeout)
    
    while ((Get-Date) -lt $endTime) {
        $readyServices = 0
        
        foreach ($service in $SERVICES) {
            if (Test-ServiceHealth -Url $service.Url) {
                $readyServices++
            }
        }
        
        $elapsed = [math]::Round(((Get-Date) - $startTime).TotalSeconds)
        $progress = [math]::Min(100, ($readyServices / $SERVICES.Count) * 100)
        
        Write-Progress -Activity '服务启动检查' -Status "已启动 $readyServices/$($SERVICES.Count) 个服务 (${elapsed}s)" -PercentComplete $progress
        
        if ($readyServices -eq $SERVICES.Count) {
            Write-Progress -Activity '服务启动检查' -Completed
            Write-StatusMessage '所有服务已启动' 'Success'
            return $true
        }
        
        Start-Sleep -Seconds 10
    }
    
    Write-Progress -Activity '服务启动检查' -Completed
    Write-StatusMessage '部分服务可能未完全启动' 'Warning'
    return $false
}

function Show-ApplicationStatus {
    Write-Host "`n=================================="
    Write-Host '🎉 JoyAgent-JDGenie 启动完成！' -ForegroundColor Green
    Write-Host "=================================="
    
    Write-Host "`n📊 服务状态：" -ForegroundColor Blue
    foreach ($service in $SERVICES) {
        $isHealthy = Test-ServiceHealth -Url $service.Url
        $status = if ($isHealthy) { '✅ 运行正常' } else { '❌ 无法访问' }
        $color = if ($isHealthy) { 'Green' } else { 'Red' }
        Write-Host "  $($service.Name): $status" -ForegroundColor $color
    }
    
    Write-Host "`n🌐 访问地址：" -ForegroundColor Blue
    Write-Host '  • 主界面: http://localhost:3000' -ForegroundColor Cyan
    Write-Host '  • 后端API: http://localhost:8080' -ForegroundColor Cyan
    Write-Host '  • 工具服务: http://localhost:1601' -ForegroundColor Cyan
    Write-Host '  • MCP客户端: http://localhost:8188' -ForegroundColor Cyan
    
    Write-Host "`n🔧 已配置的LLM服务：" -ForegroundColor Blue
    Write-Host '  • DeepSeek: ***********************************' -ForegroundColor Cyan
    Write-Host '  • 火山方舟: bcdd2ded-7352-4668-9fef-ebe31b56177a' -ForegroundColor Cyan
    Write-Host '  • Serper搜索: 04d74f4080b9379d5875e04bccc3000aa8e7840f' -ForegroundColor Cyan
    
    Write-Host "`n💡 使用提示：" -ForegroundColor Blue
    Write-Host '  • 已配置国内Docker镜像源，下载更快' -ForegroundColor Cyan
    Write-Host '  • 故障排除命令: docker-compose logs -f' -ForegroundColor Cyan
    
    Write-Host "=================================="
}

# 主执行流程
function Main {
    try {
        Write-Host '🚀 JoyAgent-JDGenie 专家级启动脚本' -ForegroundColor Blue
        Write-Host "=================================="
        
        # 执行启动流程
        Initialize-DockerConfiguration
        Restart-DockerService
        
        if (-not (Wait-DockerReady)) {
            throw 'Docker启动失败'
        }
        
        Test-DockerMirror
        Start-Application
        Wait-ServicesReady | Out-Null
        Show-ApplicationStatus
        
        # 询问是否打开浏览器
        $choice = Read-Host "`n是否打开浏览器访问主界面？(Y/N)"
        if ($choice -match '^[Yy]$') {
            Start-Process 'http://localhost:3000'
        }
        
        Write-StatusMessage '启动流程完成' 'Success'
    }
    catch {
        Write-StatusMessage "启动失败: $($_.Exception.Message)" 'Error'
        Write-Host "`n💡 故障排除建议：" -ForegroundColor Yellow
        Write-Host '  1. 检查Docker Desktop是否正常安装' -ForegroundColor Cyan
        Write-Host '  2. 确保有足够的系统资源' -ForegroundColor Cyan
        Write-Host '  3. 查看详细日志: docker-compose logs -f' -ForegroundColor Cyan
        exit 1
    }
    finally {
        Read-Host "`n按回车键退出..."
    }
}

# 执行主程序
Main
