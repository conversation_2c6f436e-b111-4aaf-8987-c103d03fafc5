@echo off
echo 🚀 Docker完整修复脚本 (增强版)
echo ==================================

echo 检查管理员权限...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 需要管理员权限运行此脚本
    echo 📂 请在目录 d:\JDAGENT\joyagent-jdgenie 中
    echo 右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限确认

echo.
echo 第1步：停止相关服务...
net stop LxssManager 2>nul
taskkill /F /IM "Docker Desktop.exe" 2>nul
timeout /t 3 /nobreak >nul

echo.
echo 第2步：启用Windows功能...
echo 正在启用WSL功能...
dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
if %errorlevel% neq 0 echo ⚠️ WSL功能启用可能失败

echo 正在启用虚拟机平台...
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
if %errorlevel% neq 0 echo ⚠️ 虚拟机平台启用可能失败

echo.
echo 第3步：启动WSL服务...
net start LxssManager
if %errorlevel% neq 0 (
    echo ⚠️ WSL服务启动失败，尝试重新启动...
    sc config LxssManager start= auto
    net start LxssManager
)

echo.
echo 第4步：下载WSL内核更新包...
if not exist "wsl_update_x64.msi" (
    echo 正在下载WSL内核更新包...
    powershell -Command "Invoke-WebRequest -Uri 'https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi' -OutFile 'wsl_update_x64.msi'"
)

echo 安装WSL内核更新包...
msiexec /i wsl_update_x64.msi /quiet /norestart
timeout /t 5 /nobreak >nul
