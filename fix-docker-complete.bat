@echo off
echo 🚀 Docker完整修复脚本
echo ==================================

echo 检查管理员权限...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 需要管理员权限运行此脚本
    echo 请右键点击脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限确认

echo.
echo 第1步：更新WSL...
wsl --update
echo WSL更新完成

echo.
echo 第2步：安装Ubuntu（如果需要）...
wsl --install -d Ubuntu --no-launch
echo Ubuntu安装完成

echo.
echo 第3步：启用必要的Windows功能...
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart

echo.
echo 第4步：设置WSL默认版本...
wsl --set-default-version 2

echo.
echo 第5步：重启Docker Desktop...
taskkill /F /IM "Docker Desktop.exe" 2>nul
timeout /t 5 /nobreak >nul
start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"

echo.
echo 第6步：等待Docker启动...
echo 正在等待Docker Desktop启动...

:wait_docker
timeout /t 5 /nobreak >nul
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo 继续等待Docker启动...
    goto wait_docker
)

echo ✅ Docker已启动！

echo.
echo 第7步：测试Docker功能...
docker run --rm hello-world
if %errorlevel% eq 0 (
    echo ✅ Docker测试成功！
) else (
    echo ❌ Docker测试失败
)

echo.
echo ==================================
echo 🎉 Docker修复完成！
echo.
echo 现在可以运行JoyAgent-JDGenie了：
echo   docker-start.sh
echo ==================================
pause
