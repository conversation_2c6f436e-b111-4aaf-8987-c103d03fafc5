# 手动启动脚本 - 分步执行
Write-Host "🔧 JoyAgent-JDGenie 手动启动脚本" -ForegroundColor Blue
Write-Host "=================================="

Write-Host "请按照提示逐步执行以下命令：" -ForegroundColor Yellow
Write-Host ""

Write-Host "第1步：配置Docker镜像源" -ForegroundColor Green
Write-Host "复制并执行以下命令：" -ForegroundColor Cyan
Write-Host ""
Write-Host '$dockerConfigDir = "$env:USERPROFILE\.docker"' -ForegroundColor White
Write-Host 'New-Item -ItemType Directory -Path $dockerConfigDir -Force' -ForegroundColor White
Write-Host ''
Write-Host '$daemonJson = @"' -ForegroundColor White
Write-Host '{' -ForegroundColor White
Write-Host '  "registry-mirrors": [' -ForegroundColor White
Write-Host '    "https://docker.m.daocloud.io",' -ForegroundColor White
Write-Host '    "https://dockerproxy.com",' -ForegroundColor White
Write-Host '    "https://mirror.baidubce.com"' -ForegroundColor White
Write-Host '  ]' -ForegroundColor White
Write-Host '}' -ForegroundColor White
Write-Host '"@' -ForegroundColor White
Write-Host ''
Write-Host '$daemonJson | Out-File "$dockerConfigDir\daemon.json" -Encoding UTF8' -ForegroundColor White

Write-Host ""
Read-Host "执行完第1步后按回车继续..."

Write-Host ""
Write-Host "第2步：重启Docker Desktop" -ForegroundColor Green
Write-Host "复制并执行以下命令：" -ForegroundColor Cyan
Write-Host ""
Write-Host 'Stop-Process -Name "Docker Desktop" -Force -ErrorAction SilentlyContinue' -ForegroundColor White
Write-Host 'Start-Sleep -Seconds 5' -ForegroundColor White
Write-Host 'Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"' -ForegroundColor White

Write-Host ""
Read-Host "执行完第2步后按回车继续..."

Write-Host ""
Write-Host "第3步：等待Docker启动" -ForegroundColor Green
Write-Host "等待2-3分钟，然后执行以下命令验证：" -ForegroundColor Cyan
Write-Host ""
Write-Host 'docker info' -ForegroundColor White
Write-Host ""
Write-Host "如果显示Docker信息，说明启动成功" -ForegroundColor Yellow

Write-Host ""
Read-Host "Docker启动成功后按回车继续..."

Write-Host ""
Write-Host "第4步：启动JoyAgent-JDGenie" -ForegroundColor Green
Write-Host "执行以下命令：" -ForegroundColor Cyan
Write-Host ""
Write-Host 'docker-compose up -d --build' -ForegroundColor White

Write-Host ""
Read-Host "执行完第4步后按回车继续..."

Write-Host ""
Write-Host "第5步：等待服务启动" -ForegroundColor Green
Write-Host "等待5-10分钟，然后访问以下地址：" -ForegroundColor Cyan
Write-Host ""
Write-Host "• 主界面: http://localhost:3000" -ForegroundColor White
Write-Host "• 后端API: http://localhost:8080" -ForegroundColor White
Write-Host "• 工具服务: http://localhost:1601" -ForegroundColor White
Write-Host "• MCP客户端: http://localhost:8188" -ForegroundColor White

Write-Host ""
Write-Host "=================================="
Write-Host "🎉 手动启动指导完成！" -ForegroundColor Green
Write-Host "=================================="

Write-Host ""
Write-Host "💡 故障排除命令：" -ForegroundColor Blue
Write-Host "• 查看日志: docker-compose logs -f" -ForegroundColor Cyan
Write-Host "• 查看容器: docker-compose ps" -ForegroundColor Cyan
Write-Host "• 重启服务: docker-compose restart" -ForegroundColor Cyan

Write-Host ""
$openBrowser = Read-Host "是否现在打开浏览器？(Y/N)"
if ($openBrowser -eq "Y" -or $openBrowser -eq "y") {
    Start-Process "http://localhost:3000"
}

Read-Host "按回车键退出..."
