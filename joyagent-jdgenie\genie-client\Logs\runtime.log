2025-08-01 12:26:59 - root - INFO - [logger.py:89] - 日志器 'None' 初始化完成，级别: DEBUG
2025-08-01 12:26:59 - asyncio - DEBUG - [proactor_events.py:633] - Using proactor: IocpProactor
2025-08-01 16:18:58 - root - INFO - [logger.py:89] - 日志器 'None' 初始化完成，级别: DEBUG
2025-08-01 16:18:58 - asyncio - DEBUG - [proactor_events.py:633] - Using proactor: IocpProactor
2025-08-01 16:28:45 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-01 16:28:45 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-01 16:28:45 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-01 16:28:45 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-01 16:28:45 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-01 16:28:45 - root - INFO - [client.py:117] - [2304272025488] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:28:45 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:28:45 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=5 socket_options=None
2025-08-01 16:28:45 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881613AD0>
2025-08-01 16:28:45 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A85F0> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-01 16:28:45 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002188151FF50>
2025-08-01 16:28:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-01 16:28:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:28:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-01 16:28:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:28:45 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Fri, 01 Aug 2025 08:28:46 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117540369265737214e0066bf681b395cf0a1dd90fc674b46227dfe;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117540369265737214e0066bf681b395cf0a1dd90fc674b46227dfe;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'6c5e811e-f18d-4ed6-b0cc-5edfb9362157'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:28:46 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=2f38e481e9f24bfd9c661fb945fd32d1
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=2f38e481e9f24bfd9c661fb945fd32d1
2025-08-01 16:28:46 - root - DEBUG - [client.py:129] - [2304272025488] SSE流连接已建立
2025-08-01 16:28:46 - root - DEBUG - [client.py:134] - [2304272025488] 客户端会话已创建
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-01 16:28:46 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 16:28:46 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000218816FB590>
2025-08-01 16:28:46 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A85F0> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-01 16:28:46 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881580E10>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:28:46 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'aa623c37-d63b-474a-8a74-832b109eab62'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:28:46 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=2f38e481e9f24bfd9c661fb945fd32d1 "HTTP/1.1 202 Accepted"
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-01 16:28:46 - root - INFO - [client.py:138] - [2304272025488] SSE连接建立成功
2025-08-01 16:28:46 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:28:47 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'f1feaf87-675e-487d-a8f5-4f2f6efd4cd4'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:28:46 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=2f38e481e9f24bfd9c661fb945fd32d1 "HTTP/1.1 202 Accepted"
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:28:47 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'100585f8-5a20-4704-85e5-b4e6482e11b6'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:28:46 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=2f38e481e9f24bfd9c661fb945fd32d1 "HTTP/1.1 202 Accepted"
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:28:46 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-01 16:28:46 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-01 16:28:46 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-01 16:28:46 - root - DEBUG - [client.py:214] - [2304272025488] 开始清理SSE连接资源...
2025-08-01 16:28:46 - root - DEBUG - [client.py:219] - [2304272025488] 会话上下文已清理
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 218804df310')
2025-08-01 16:28:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:28:46 - httpcore.connection - DEBUG - [_trace.py:87] - close.started
2025-08-01 16:28:46 - httpcore.connection - DEBUG - [_trace.py:87] - close.complete
2025-08-01 16:28:46 - root - DEBUG - [client.py:228] - [2304272025488] 上下文已清理
2025-08-01 16:28:46 - root - INFO - [client.py:234] - [2304272025488] SSE连接资源清理完成
2025-08-01 16:33:22 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-01 16:33:22 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-01 16:33:22 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-01 16:33:22 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-01 16:33:22 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-01 16:33:22 - root - INFO - [client.py:117] - [2304273218832] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:33:22 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:33:23 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=5 socket_options=None
2025-08-01 16:33:23 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002188162FF50>
2025-08-01 16:33:23 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A9370> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-01 16:33:23 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002188162F950>
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Fri, 01 Aug 2025 08:33:24 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117540372040663618e006ef6223a9992a59f70e94cc158f8622f54;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117540372040663618e006ef6223a9992a59f70e94cc158f8622f54;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'bd926fd3-cfdb-4d74-a16b-dfef58077486'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:33:23 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-01 16:33:23 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-01 16:33:23 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-01 16:33:23 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=567257b6e6dc4d65ac36dcaa6e783618
2025-08-01 16:33:23 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=567257b6e6dc4d65ac36dcaa6e783618
2025-08-01 16:33:23 - root - DEBUG - [client.py:129] - [2304273218832] SSE流连接已建立
2025-08-01 16:33:23 - root - DEBUG - [client.py:134] - [2304273218832] 客户端会话已创建
2025-08-01 16:33:23 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-01 16:33:23 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 16:33:23 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002188162CB90>
2025-08-01 16:33:23 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A9370> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-01 16:33:23 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881624BD0>
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:33:24 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'20279dd5-b097-41a4-a6d1-1cc4132fae4e'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:33:23 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=567257b6e6dc4d65ac36dcaa6e783618 "HTTP/1.1 202 Accepted"
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:33:23 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:33:23 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:33:23 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-01 16:33:23 - root - INFO - [client.py:138] - [2304273218832] SSE连接建立成功
2025-08-01 16:33:23 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-01 16:33:23 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:33:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:33:24 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'540857e1-128f-4194-a0f3-f05aebe6b39f'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:33:24 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=567257b6e6dc4d65ac36dcaa6e783618 "HTTP/1.1 202 Accepted"
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:33:24 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:33:24 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:33:24 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'a9cd93bf-260a-4205-b628-ed6297034394'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:33:24 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=567257b6e6dc4d65ac36dcaa6e783618 "HTTP/1.1 202 Accepted"
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:33:24 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:33:24 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:33:24 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-01 16:33:24 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-01 16:33:24 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-01 16:33:24 - root - DEBUG - [client.py:214] - [2304273218832] 开始清理SSE连接资源...
2025-08-01 16:33:24 - root - DEBUG - [client.py:219] - [2304273218832] 会话上下文已清理
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 21881580690')
2025-08-01 16:33:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:33:24 - httpcore.connection - DEBUG - [_trace.py:87] - close.started
2025-08-01 16:33:24 - httpcore.connection - DEBUG - [_trace.py:87] - close.complete
2025-08-01 16:33:24 - root - DEBUG - [client.py:228] - [2304273218832] 上下文已清理
2025-08-01 16:33:24 - root - INFO - [client.py:234] - [2304273218832] SSE连接资源清理完成
2025-08-01 16:36:12 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-01 16:36:12 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-01 16:36:12 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-01 16:36:12 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-01 16:36:12 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-01 16:36:12 - root - INFO - [client.py:117] - [2304273218832] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:36:12 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:36:13 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=5 socket_options=None
2025-08-01 16:36:13 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881582B50>
2025-08-01 16:36:13 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A9640> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-01 16:36:13 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881582C10>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Fri, 01 Aug 2025 08:36:14 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820517540373739507917e3fa5b9b00479e758d3ce485c1ec7103c2f9a;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820517540373739507917e3fa5b9b00479e758d3ce485c1ec7103c2f9a;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'223c8df2-ebe5-4a23-8353-a61b2cb54047'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:36:13 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-01 16:36:13 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-01 16:36:13 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-01 16:36:13 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=f6c0c0d563d6498696501975e94d09e9
2025-08-01 16:36:13 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=f6c0c0d563d6498696501975e94d09e9
2025-08-01 16:36:13 - root - DEBUG - [client.py:129] - [2304273218832] SSE流连接已建立
2025-08-01 16:36:13 - root - DEBUG - [client.py:134] - [2304273218832] 客户端会话已创建
2025-08-01 16:36:13 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-01 16:36:13 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 16:36:13 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000218817768D0>
2025-08-01 16:36:13 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A9640> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-01 16:36:13 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000218816FA590>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:36:14 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'234eaeff-97a4-4f99-bb7f-bcf75d87927b'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:36:13 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=f6c0c0d563d6498696501975e94d09e9 "HTTP/1.1 202 Accepted"
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:36:13 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:36:13 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:36:13 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-01 16:36:13 - root - INFO - [client.py:138] - [2304273218832] SSE连接建立成功
2025-08-01 16:36:13 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-01 16:36:13 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:36:14 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'98970fd7-7f63-477c-89d6-5fbc13a0a20b'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:36:13 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=f6c0c0d563d6498696501975e94d09e9 "HTTP/1.1 202 Accepted"
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:36:13 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:36:13 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:36:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:36:14 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:36:14 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'6bbd27cf-d348-4a37-b0c1-1aac20dca43d'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:36:14 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=f6c0c0d563d6498696501975e94d09e9 "HTTP/1.1 202 Accepted"
2025-08-01 16:36:14 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:36:14 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:36:14 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:36:14 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:36:14 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:36:14 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:36:14 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-01 16:36:14 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-01 16:36:14 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-01 16:36:14 - root - DEBUG - [client.py:214] - [2304273218832] 开始清理SSE连接资源...
2025-08-01 16:36:14 - root - DEBUG - [client.py:219] - [2304273218832] 会话上下文已清理
2025-08-01 16:36:14 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:36:14 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 2188160e990')
2025-08-01 16:36:14 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:36:14 - httpcore.connection - DEBUG - [_trace.py:87] - close.started
2025-08-01 16:36:14 - httpcore.connection - DEBUG - [_trace.py:87] - close.complete
2025-08-01 16:36:14 - root - DEBUG - [client.py:228] - [2304273218832] 上下文已清理
2025-08-01 16:36:14 - root - INFO - [client.py:234] - [2304273218832] SSE连接资源清理完成
2025-08-01 16:42:23 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-01 16:42:23 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-01 16:42:23 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-01 16:42:23 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-01 16:42:23 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-01 16:42:23 - root - INFO - [client.py:117] - [2304274559952] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:42:23 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:42:23 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=5 socket_options=None
2025-08-01 16:42:23 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881774AD0>
2025-08-01 16:42:23 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A9760> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-01 16:42:23 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000218815A7550>
2025-08-01 16:42:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-01 16:42:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:42:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-01 16:42:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:42:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Fri, 01 Aug 2025 08:42:24 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117540377445557616e0054c53519326a468cb22857fbb0022ca675;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117540377445557616e0054c53519326a468cb22857fbb0022ca675;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'90d7542e-32cc-43ec-9660-b3ed24090d2d'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:42:24 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=f9dd15127daf4e208bba893e5ea028e2
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=f9dd15127daf4e208bba893e5ea028e2
2025-08-01 16:42:24 - root - DEBUG - [client.py:129] - [2304274559952] SSE流连接已建立
2025-08-01 16:42:24 - root - DEBUG - [client.py:134] - [2304274559952] 客户端会话已创建
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-01 16:42:24 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 16:42:24 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002188162CC50>
2025-08-01 16:42:24 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A9760> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-01 16:42:24 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002188162C210>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:42:24 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'f1899b03-1ab7-4428-8c8f-413eded37ba8'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:42:24 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=f9dd15127daf4e208bba893e5ea028e2 "HTTP/1.1 202 Accepted"
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:42:24 - root - INFO - [client.py:138] - [2304274559952] SSE连接建立成功
2025-08-01 16:42:24 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:42:25 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'8dd07429-e1b3-4101-950b-8ab5590a23c6'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:42:24 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=f9dd15127daf4e208bba893e5ea028e2 "HTTP/1.1 202 Accepted"
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:42:25 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'd4e3a871-998f-480d-8b5b-df3e5b7aaa0c'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:42:24 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=f9dd15127daf4e208bba893e5ea028e2 "HTTP/1.1 202 Accepted"
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:42:24 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-01 16:42:24 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-01 16:42:24 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-01 16:42:24 - root - DEBUG - [client.py:214] - [2304274559952] 开始清理SSE连接资源...
2025-08-01 16:42:24 - root - DEBUG - [client.py:219] - [2304274559952] 会话上下文已清理
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 2188163fc50')
2025-08-01 16:42:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:42:24 - httpcore.connection - DEBUG - [_trace.py:87] - close.started
2025-08-01 16:42:24 - httpcore.connection - DEBUG - [_trace.py:87] - close.complete
2025-08-01 16:42:24 - root - DEBUG - [client.py:228] - [2304274559952] 上下文已清理
2025-08-01 16:42:24 - root - INFO - [client.py:234] - [2304274559952] SSE连接资源清理完成
2025-08-01 16:48:11 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-01 16:48:11 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-01 16:48:11 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-01 16:48:11 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-01 16:48:11 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-01 16:48:11 - root - INFO - [client.py:117] - [2304274559952] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:48:11 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:48:12 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=5 socket_options=None
2025-08-01 16:48:12 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002188163D910>
2025-08-01 16:48:12 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816AA690> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-01 16:48:12 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000218815A7290>
2025-08-01 16:48:12 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-01 16:48:12 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:48:12 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-01 16:48:12 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:48:12 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 16:48:12 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Fri, 01 Aug 2025 08:48:13 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820517540380932306822e3fbe2e0d40afde89611c13250ce73bf14d3b;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820517540380932306822e3fbe2e0d40afde89611c13250ce73bf14d3b;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'd3b302e1-d2cb-400b-a5de-6e63ac5e3e34'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:48:12 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-01 16:48:12 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-01 16:48:12 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-01 16:48:12 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-01 16:48:12 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=8d02abbc096243c7bc23634c01988998
2025-08-01 16:48:12 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=8d02abbc096243c7bc23634c01988998
2025-08-01 16:48:12 - root - DEBUG - [client.py:129] - [2304274559952] SSE流连接已建立
2025-08-01 16:48:12 - root - DEBUG - [client.py:134] - [2304274559952] 客户端会话已创建
2025-08-01 16:48:12 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-01 16:48:12 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 16:48:12 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002188162C7D0>
2025-08-01 16:48:12 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816AA690> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-01 16:48:12 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881696090>
2025-08-01 16:48:12 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:48:12 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:48:12 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:48:12 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:48:12 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:48:13 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:48:13 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:48:13 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'80d3cbbe-3537-4cf7-8386-fd4d95452dc1'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:48:13 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=8d02abbc096243c7bc23634c01988998 "HTTP/1.1 202 Accepted"
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:48:13 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:48:13 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-01 16:48:13 - root - INFO - [client.py:138] - [2304274559952] SSE连接建立成功
2025-08-01 16:48:13 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:48:13 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'12aa9e1d-c170-4db1-bd26-5d7edd147577'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:48:13 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=8d02abbc096243c7bc23634c01988998 "HTTP/1.1 202 Accepted"
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:48:13 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:48:13 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:48:14 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'dea01f38-1a0a-4c3f-b6d5-4cf60fec5b15'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:48:13 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=8d02abbc096243c7bc23634c01988998 "HTTP/1.1 202 Accepted"
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:48:13 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:48:13 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:48:13 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-01 16:48:13 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-01 16:48:13 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-01 16:48:13 - root - DEBUG - [client.py:214] - [2304274559952] 开始清理SSE连接资源...
2025-08-01 16:48:13 - root - DEBUG - [client.py:219] - [2304274559952] 会话上下文已清理
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 21881767a50')
2025-08-01 16:48:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:48:13 - httpcore.connection - DEBUG - [_trace.py:87] - close.started
2025-08-01 16:48:13 - httpcore.connection - DEBUG - [_trace.py:87] - close.complete
2025-08-01 16:48:13 - root - DEBUG - [client.py:228] - [2304274559952] 上下文已清理
2025-08-01 16:48:13 - root - INFO - [client.py:234] - [2304274559952] SSE连接资源清理完成
2025-08-01 16:50:46 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-01 16:50:46 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-01 16:50:46 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-01 16:50:46 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-01 16:50:46 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-01 16:50:46 - root - INFO - [client.py:117] - [2304273746384] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:50:46 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:50:46 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=5 socket_options=None
2025-08-01 16:50:46 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881767BD0>
2025-08-01 16:50:46 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A9AC0> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-01 16:50:46 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002188162FF90>
2025-08-01 16:50:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-01 16:50:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:50:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-01 16:50:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:50:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Fri, 01 Aug 2025 08:50:47 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820517540382476017661e3fc7cbc222736900064e5e9d43eb5c0abc7e;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820517540382476017661e3fc7cbc222736900064e5e9d43eb5c0abc7e;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'40e17b76-4484-409e-9b2d-7eee49f08de4'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:50:47 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=7cbaae966088419c99d3bb6aab0c5c3f
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=7cbaae966088419c99d3bb6aab0c5c3f
2025-08-01 16:50:47 - root - DEBUG - [client.py:129] - [2304273746384] SSE流连接已建立
2025-08-01 16:50:47 - root - DEBUG - [client.py:134] - [2304273746384] 客户端会话已创建
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-01 16:50:47 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 16:50:47 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000218816665D0>
2025-08-01 16:50:47 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A9AC0> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-01 16:50:47 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881573CD0>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:50:48 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'8f4885d5-51da-40e8-8b16-9a589ce56bea'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:50:47 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=7cbaae966088419c99d3bb6aab0c5c3f "HTTP/1.1 202 Accepted"
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-01 16:50:47 - root - INFO - [client.py:138] - [2304273746384] SSE连接建立成功
2025-08-01 16:50:47 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:50:48 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'a1dc9514-f69d-4069-a402-6292ccb24a12'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:50:47 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=7cbaae966088419c99d3bb6aab0c5c3f "HTTP/1.1 202 Accepted"
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:50:48 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'fe829c79-1100-4b06-964c-f6762ea4a57f'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:50:47 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=7cbaae966088419c99d3bb6aab0c5c3f "HTTP/1.1 202 Accepted"
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:50:47 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:50:47 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-01 16:50:47 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-01 16:50:47 - root - DEBUG - [client.py:214] - [2304273746384] 开始清理SSE连接资源...
2025-08-01 16:50:47 - root - DEBUG - [client.py:219] - [2304273746384] 会话上下文已清理
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 2188162d190')
2025-08-01 16:50:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:50:47 - httpcore.connection - DEBUG - [_trace.py:87] - close.started
2025-08-01 16:50:47 - httpcore.connection - DEBUG - [_trace.py:87] - close.complete
2025-08-01 16:50:47 - root - DEBUG - [client.py:228] - [2304273746384] 上下文已清理
2025-08-01 16:50:47 - root - INFO - [client.py:234] - [2304273746384] SSE连接资源清理完成
2025-08-01 16:52:07 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-01 16:52:07 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-01 16:52:07 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-01 16:52:07 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-01 16:52:07 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-01 16:52:07 - root - INFO - [client.py:117] - [2304273208080] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:52:07 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 16:52:08 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=5 socket_options=None
2025-08-01 16:52:08 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881767A10>
2025-08-01 16:52:08 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A92E0> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-01 16:52:08 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881666490>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Fri, 01 Aug 2025 08:52:09 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820917540383291288824e2da22fc67b95dc91a400dc61b815fe292e0b;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820917540383291288824e2da22fc67b95dc91a400dc61b815fe292e0b;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'37808407-8c28-4196-a88a-0a7cd23c7506'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:52:08 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-01 16:52:08 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-01 16:52:08 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-01 16:52:08 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=4721ddaa54fd47aa93a989fc144413f3
2025-08-01 16:52:08 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=4721ddaa54fd47aa93a989fc144413f3
2025-08-01 16:52:08 - root - DEBUG - [client.py:129] - [2304273208080] SSE流连接已建立
2025-08-01 16:52:08 - root - DEBUG - [client.py:134] - [2304273208080] 客户端会话已创建
2025-08-01 16:52:08 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-01 16:52:08 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 16:52:08 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881607650>
2025-08-01 16:52:08 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A92E0> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-01 16:52:08 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881607550>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:52:09 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'566858fa-ba92-4f39-87fc-840d957a6d72'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:52:08 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=4721ddaa54fd47aa93a989fc144413f3 "HTTP/1.1 202 Accepted"
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:52:08 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:52:08 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:52:08 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-01 16:52:08 - root - INFO - [client.py:138] - [2304273208080] SSE连接建立成功
2025-08-01 16:52:08 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-01 16:52:08 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:52:09 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'9f72bedd-246f-4e3c-aa32-dfbef503b26a'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:52:08 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=4721ddaa54fd47aa93a989fc144413f3 "HTTP/1.1 202 Accepted"
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:52:08 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:52:08 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 16:52:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 16:52:09 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 08:52:09 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'bc0097af-abd5-4dff-bde4-b8727c08be9d'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 16:52:09 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=4721ddaa54fd47aa93a989fc144413f3 "HTTP/1.1 202 Accepted"
2025-08-01 16:52:09 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 16:52:09 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 16:52:09 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:52:09 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:52:09 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 16:52:09 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 16:52:09 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-01 16:52:09 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-01 16:52:09 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-01 16:52:09 - root - DEBUG - [client.py:214] - [2304273208080] 开始清理SSE连接资源...
2025-08-01 16:52:09 - root - DEBUG - [client.py:219] - [2304273208080] 会话上下文已清理
2025-08-01 16:52:09 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 16:52:09 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 2188162fc50')
2025-08-01 16:52:09 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 16:52:09 - httpcore.connection - DEBUG - [_trace.py:87] - close.started
2025-08-01 16:52:09 - httpcore.connection - DEBUG - [_trace.py:87] - close.complete
2025-08-01 16:52:09 - root - DEBUG - [client.py:228] - [2304273208080] 上下文已清理
2025-08-01 16:52:09 - root - INFO - [client.py:234] - [2304273208080] SSE连接资源清理完成
2025-08-01 17:02:02 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-01 17:02:02 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-01 17:02:02 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-01 17:02:02 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-01 17:02:02 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-01 17:02:02 - root - INFO - [client.py:117] - [2304274042256] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 17:02:02 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 17:02:02 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=5 socket_options=None
2025-08-01 17:02:02 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000218816070D0>
2025-08-01 17:02:02 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816AA570> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-01 17:02:03 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000218817770D0>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Fri, 01 Aug 2025 09:02:03 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117540389237423222e005b18e03affc9b1db951c51a4c1272140ab;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117540389237423222e005b18e03affc9b1db951c51a4c1272140ab;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'04d49f70-96a7-4949-8fce-81a62c6f1ebd'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 17:02:03 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=90940654bbf9448bb716402b1f789f55
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=90940654bbf9448bb716402b1f789f55
2025-08-01 17:02:03 - root - DEBUG - [client.py:129] - [2304274042256] SSE流连接已建立
2025-08-01 17:02:03 - root - DEBUG - [client.py:134] - [2304274042256] 客户端会话已创建
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-01 17:02:03 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 17:02:03 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002188160C9D0>
2025-08-01 17:02:03 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816AA570> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-01 17:02:03 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881776810>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 09:02:04 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'bb61a0a3-6c1a-4d57-ba61-5f31aa37a4c6'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 17:02:03 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=90940654bbf9448bb716402b1f789f55 "HTTP/1.1 202 Accepted"
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-01 17:02:03 - root - INFO - [client.py:138] - [2304274042256] SSE连接建立成功
2025-08-01 17:02:03 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 09:02:04 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'5e2a2c0a-9bac-4351-8fec-24f26bad7129'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 17:02:03 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=90940654bbf9448bb716402b1f789f55 "HTTP/1.1 202 Accepted"
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 09:02:04 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'f8ba7009-8cf4-4633-8ad8-eb5401c2f01a'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 17:02:03 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=90940654bbf9448bb716402b1f789f55 "HTTP/1.1 202 Accepted"
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 17:02:03 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-01 17:02:03 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-01 17:02:03 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-01 17:02:03 - root - DEBUG - [client.py:214] - [2304274042256] 开始清理SSE连接资源...
2025-08-01 17:02:03 - root - DEBUG - [client.py:219] - [2304274042256] 会话上下文已清理
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 218816fb690')
2025-08-01 17:02:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 17:02:03 - httpcore.connection - DEBUG - [_trace.py:87] - close.started
2025-08-01 17:02:03 - httpcore.connection - DEBUG - [_trace.py:87] - close.complete
2025-08-01 17:02:03 - root - DEBUG - [client.py:228] - [2304274042256] 上下文已清理
2025-08-01 17:02:03 - root - INFO - [client.py:234] - [2304274042256] SSE连接资源清理完成
2025-08-01 17:02:58 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-01 17:02:58 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-01 17:02:58 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-01 17:02:58 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-01 17:02:58 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-01 17:02:58 - root - INFO - [client.py:117] - [2304273079632] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 17:02:58 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-01 17:02:59 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=5 socket_options=None
2025-08-01 17:02:59 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000218816B2810>
2025-08-01 17:02:59 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A8CB0> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-01 17:02:59 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881624590>
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Fri, 01 Aug 2025 09:03:00 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820017540389801746360e1e9bcceb62ee7fed8f58d72856a73addc1f7;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820017540389801746360e1e9bcceb62ee7fed8f58d72856a73addc1f7;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'5e7598a5-bc8f-44bc-a598-5a54a5ac48e0'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 17:02:59 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-01 17:02:59 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-01 17:02:59 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-01 17:02:59 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=c1a189054c424179a2c58287920f059c
2025-08-01 17:02:59 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=c1a189054c424179a2c58287920f059c
2025-08-01 17:02:59 - root - DEBUG - [client.py:129] - [2304273079632] SSE流连接已建立
2025-08-01 17:02:59 - root - DEBUG - [client.py:134] - [2304273079632] 客户端会话已创建
2025-08-01 17:02:59 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-01 17:02:59 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='mcp.api-inference.modelscope.net' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-01 17:02:59 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002188160F0D0>
2025-08-01 17:02:59 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000218816A8CB0> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-01 17:02:59 - httpcore.connection - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000021881624850>
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 17:02:59 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 17:02:59 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 09:03:00 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'1699b852-fbe1-4ccd-a4e5-b854f72b30c3'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 17:02:59 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c1a189054c424179a2c58287920f059c "HTTP/1.1 202 Accepted"
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 17:02:59 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 17:02:59 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-01 17:02:59 - root - INFO - [client.py:138] - [2304273079632] SSE连接建立成功
2025-08-01 17:02:59 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 17:02:59 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 09:03:00 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'8f47ee0a-18ce-40c3-9c1a-22e82085a8d5'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 17:03:00 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c1a189054c424179a2c58287920f059c "HTTP/1.1 202 Accepted"
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 17:03:00 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 17:03:00 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Fri, 01 Aug 2025 09:03:00 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'053fbbe6-bd7f-4137-96f2-77c95a1a6cf5'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-01 17:03:00 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c1a189054c424179a2c58287920f059c "HTTP/1.1 202 Accepted"
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 17:03:00 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-01 17:03:00 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-01 17:03:00 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-01 17:03:00 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-01 17:03:00 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-01 17:03:00 - root - DEBUG - [client.py:214] - [2304273079632] 开始清理SSE连接资源...
2025-08-01 17:03:00 - root - DEBUG - [client.py:219] - [2304273079632] 会话上下文已清理
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 218816fbed0')
2025-08-01 17:03:00 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-01 17:03:00 - httpcore.connection - DEBUG - [_trace.py:87] - close.started
2025-08-01 17:03:00 - httpcore.connection - DEBUG - [_trace.py:87] - close.complete
2025-08-01 17:03:00 - root - DEBUG - [client.py:228] - [2304273079632] 上下文已清理
2025-08-01 17:03:00 - root - INFO - [client.py:234] - [2304273079632] SSE连接资源清理完成
