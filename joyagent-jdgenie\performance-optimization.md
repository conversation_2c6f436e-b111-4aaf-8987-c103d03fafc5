# 🚀 JoyAgent-JDGenie 性能优化指南

## Docker Desktop 优化配置

### 1. 资源分配优化

**推荐配置**：
- **内存**: 8GB (最低4GB)
- **CPU**: 4核 (最低2核)
- **磁盘**: 50GB可用空间
- **交换文件**: 2GB

**配置步骤**：
1. 打开Docker Desktop
2. Settings → Resources → Advanced
3. 调整内存和CPU分配
4. 启用 "Use the WSL 2 based engine" (Windows)

### 2. 存储优化

**启用BuildKit**：
```bash
# 设置环境变量
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# 或在 ~/.docker/config.json 中添加：
{
  "features": {
    "buildkit": true
  }
}
```

**配置镜像缓存**：
```bash
# 创建 ~/.docker/daemon.json
{
  "registry-mirrors": [
    "https://docker.m.daocloud.io",
    "https://registry.docker-cn.com"
  ],
  "storage-driver": "overlay2",
  "storage-opts": [
    "overlay2.override_kernel_check=true"
  ]
}
```

## 应用层面优化

### 1. 环境变量优化

**生产环境配置**：
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  genie-app:
    environment:
      # JVM优化
      - JAVA_OPTS=-Xms2g -Xmx4g -XX:+UseG1GC
      
      # Node.js优化
      - NODE_ENV=production
      - NODE_OPTIONS=--max-old-space-size=4096
      
      # Python优化
      - PYTHONUNBUFFERED=1
      - PYTHONOPTIMIZE=1
      
      # 数据库连接池
      - DB_POOL_SIZE=20
      - DB_MAX_CONNECTIONS=100
```

### 2. 资源限制配置

**内存和CPU限制**：
```yaml
services:
  genie-app:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
```

## 网络优化

### 1. 自定义网络配置

```yaml
networks:
  genie-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********

services:
  genie-app:
    networks:
      genie-network:
        ipv4_address: **********0
```

### 2. DNS优化

```yaml
services:
  genie-app:
    dns:
      - *******
      - *******
    dns_search:
      - local
```

## 存储优化

### 1. 数据卷优化

```yaml
volumes:
  genie-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /path/to/fast/storage

services:
  genie-app:
    volumes:
      - genie-data:/app/data
      - type: tmpfs
        target: /tmp
        tmpfs:
          size: 1G
```

### 2. 缓存策略

**多阶段构建优化**：
```dockerfile
# 优化的Dockerfile示例
FROM node:18-alpine as frontend-builder
WORKDIR /app
# 先复制package文件，利用Docker层缓存
COPY ui/package*.json ./
RUN npm ci --only=production
COPY ui/ .
RUN npm run build

FROM openjdk:17-jre-slim as backend-runner
# 使用更小的基础镜像
WORKDIR /app
COPY --from=backend-builder /app/target/*.jar app.jar
# 优化JVM参数
ENV JAVA_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC -XX:+UseContainerSupport"
CMD ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

## 监控和调优

### 1. 性能监控脚本

```bash
#!/bin/bash
# performance-monitor.sh

echo "=== JoyAgent-JDGenie 性能监控 ==="
echo "时间: $(date)"
echo ""

echo "=== 容器资源使用 ==="
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

echo ""
echo "=== 系统负载 ==="
docker-compose exec genie-app uptime

echo ""
echo "=== 内存详情 ==="
docker-compose exec genie-app free -h

echo ""
echo "=== 磁盘使用 ==="
docker-compose exec genie-app df -h

echo ""
echo "=== 网络连接 ==="
docker-compose exec genie-app netstat -an | grep LISTEN

echo ""
echo "=== 进程状态 ==="
docker-compose exec genie-app ps aux --sort=-%cpu | head -10
```

### 2. 自动化调优脚本

```bash
#!/bin/bash
# auto-tune.sh

echo "🔧 自动性能调优..."

# 清理未使用的资源
echo "清理Docker缓存..."
docker system prune -f

# 优化内存使用
echo "优化内存设置..."
docker-compose exec genie-app sysctl vm.swappiness=10

# 调整文件描述符限制
echo "调整系统限制..."
docker-compose exec genie-app ulimit -n 65536

# 重启服务以应用优化
echo "重启服务..."
docker-compose restart

echo "✅ 调优完成"
```

## 负载测试

### 1. 简单负载测试

```bash
#!/bin/bash
# load-test.sh

echo "🧪 负载测试开始..."

# 测试前端响应
echo "测试前端服务..."
for i in {1..10}; do
  time curl -s http://localhost:3000 > /dev/null
done

# 测试API响应
echo "测试API服务..."
for i in {1..10}; do
  time curl -s http://localhost:8080/web/health > /dev/null
done

# 并发测试
echo "并发测试..."
ab -n 100 -c 10 http://localhost:3000/

echo "✅ 负载测试完成"
```

### 2. 压力测试监控

```bash
#!/bin/bash
# stress-monitor.sh

echo "🔥 压力测试监控..."

# 启动监控
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" &
MONITOR_PID=$!

# 运行压力测试
ab -n 1000 -c 50 http://localhost:3000/ &
TEST_PID=$!

# 等待测试完成
wait $TEST_PID

# 停止监控
kill $MONITOR_PID

echo "✅ 压力测试完成"
```

## 生产环境优化

### 1. 生产配置文件

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  genie-app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    restart: always
    environment:
      - NODE_ENV=production
      - JAVA_OPTS=-Xms2g -Xmx4g -XX:+UseG1GC
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - genie-app
    restart: always
```

### 2. Nginx反向代理配置

```nginx
# nginx.conf
upstream genie-backend {
    server genie-app:8080;
    keepalive 32;
}

upstream genie-frontend {
    server genie-app:3000;
    keepalive 32;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态资源
    location / {
        proxy_pass http://genie-frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_cache_valid 200 1h;
    }
    
    # API请求
    location /api/ {
        proxy_pass http://genie-backend/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
```

## 性能基准测试

### 1. 基准测试脚本

```bash
#!/bin/bash
# benchmark.sh

echo "📊 JoyAgent-JDGenie 基准测试"
echo "=================================="

# 记录开始时间
START_TIME=$(date +%s)

# 测试项目1: 启动时间
echo "测试1: 服务启动时间"
docker-compose down > /dev/null 2>&1
START_UP=$(date +%s)
docker-compose up -d > /dev/null 2>&1

# 等待服务就绪
while ! curl -s http://localhost:3000 > /dev/null; do
  sleep 1
done

STARTUP_TIME=$(($(date +%s) - START_UP))
echo "启动时间: ${STARTUP_TIME}秒"

# 测试项目2: 响应时间
echo "测试2: 平均响应时间"
RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:3000)
echo "前端响应时间: ${RESPONSE_TIME}秒"

API_RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:8080/web/health)
echo "API响应时间: ${API_RESPONSE_TIME}秒"

# 测试项目3: 内存使用
echo "测试3: 内存使用情况"
MEMORY_USAGE=$(docker stats --no-stream --format "{{.MemUsage}}" joyagent-jdgenie)
echo "内存使用: $MEMORY_USAGE"

# 测试项目4: CPU使用
echo "测试4: CPU使用情况"
CPU_USAGE=$(docker stats --no-stream --format "{{.CPUPerc}}" joyagent-jdgenie)
echo "CPU使用: $CPU_USAGE"

TOTAL_TIME=$(($(date +%s) - START_TIME))
echo ""
echo "=================================="
echo "总测试时间: ${TOTAL_TIME}秒"
echo "=================================="
```

通过这些优化措施，您可以显著提升JoyAgent-JDGenie的性能和稳定性。建议根据实际使用情况逐步应用这些优化配置。
