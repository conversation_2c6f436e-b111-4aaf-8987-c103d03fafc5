# 🔧 JoyAgent-JDGenie Docker 故障排除指南

## 常见问题分类

### 1. Docker环境问题

#### 问题：Docker Desktop未启动
**症状**：
```
error during connect: this error may indicate that the docker daemon is not running
```

**解决方案**：
1. 启动Docker Desktop应用程序
2. 等待Docker完全启动（托盘图标变绿）
3. 验证：`docker info`

#### 问题：权限不足
**症状**：
```
permission denied while trying to connect to the Docker daemon socket
```

**解决方案**：
- Windows: 确保用户在docker-users组中
- 重启Docker Desktop
- 以管理员身份运行命令提示符

### 2. 构建问题

#### 问题：网络连接超时
**症状**：
```
failed to solve with frontend dockerfile.v0: failed to create LLB definition
```

**解决方案**：
```bash
# 配置Docker镜像源
# 创建或编辑 ~/.docker/daemon.json
{
  "registry-mirrors": [
    "https://docker.m.daocloud.io",
    "https://registry.docker-cn.com"
  ]
}

# 重启Docker Desktop
```

#### 问题：磁盘空间不足
**症状**：
```
no space left on device
```

**解决方案**：
```bash
# 清理Docker缓存
docker system prune -a --volumes

# 清理未使用的镜像
docker image prune -a

# 检查磁盘使用
docker system df
```

### 3. 启动问题

#### 问题：端口被占用
**症状**：
```
bind: address already in use
```

**解决方案**：
```bash
# 查找占用端口的进程
netstat -ano | findstr :3000
netstat -ano | findstr :8080

# 终止进程或修改端口映射
# 编辑 docker-compose.yml
ports:
  - "3001:3000"  # 改为其他端口
```

#### 问题：容器启动后立即退出
**症状**：
```
Exited (1) 2 seconds ago
```

**解决方案**：
```bash
# 查看详细日志
docker-compose logs genie-app

# 检查配置文件
docker-compose config

# 进入容器调试
docker-compose run --rm genie-app bash
```

### 4. 服务访问问题

#### 问题：前端页面无法访问
**症状**：页面显示"无法访问此网站"

**解决方案**：
```bash
# 检查容器状态
docker-compose ps

# 检查端口映射
docker port joyagent-jdgenie

# 测试内部连接
docker-compose exec genie-app curl http://localhost:3000
```

#### 问题：API调用失败
**症状**：前端显示网络错误

**解决方案**：
```bash
# 检查后端服务
curl http://localhost:8080/web/health

# 查看后端日志
docker-compose logs genie-app | grep -i error

# 检查环境变量
docker-compose exec genie-app env | grep API
```

### 5. 性能问题

#### 问题：服务响应缓慢
**症状**：页面加载时间过长

**解决方案**：
```bash
# 检查资源使用
docker stats

# 增加Docker Desktop内存分配
# Settings -> Resources -> Memory (建议8GB+)

# 检查容器健康状态
docker-compose exec genie-app top
```

#### 问题：内存不足
**症状**：
```
OOMKilled
```

**解决方案**：
```bash
# 增加内存限制
# 在 docker-compose.yml 中添加：
services:
  genie-app:
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
```

## 调试工具和技巧

### 1. 日志分析
```bash
# 实时查看所有日志
docker-compose logs -f

# 过滤错误日志
docker-compose logs | grep -i error

# 查看特定时间段日志
docker-compose logs --since="1h"
```

### 2. 容器调试
```bash
# 进入运行中的容器
docker-compose exec genie-app bash

# 检查进程状态
docker-compose exec genie-app ps aux

# 检查网络连接
docker-compose exec genie-app netstat -tlnp
```

### 3. 配置验证
```bash
# 验证docker-compose配置
docker-compose config

# 检查环境变量
docker-compose exec genie-app printenv

# 测试服务连通性
docker-compose exec genie-app curl -I http://localhost:3000
```

## 完全重置指南

### 场景1：保留数据的重置
```bash
# 停止服务
docker-compose down

# 重新构建并启动
docker-compose up -d --build
```

### 场景2：完全清理重置
```bash
# 停止并删除所有内容
docker-compose down -v

# 清理Docker缓存
docker system prune -a --volumes

# 重新构建启动
docker-compose up -d --build
```

### 场景3：仅重置配置
```bash
# 停止服务
docker-compose down

# 删除容器但保留数据卷
docker-compose rm -f

# 重新启动
docker-compose up -d
```

## 获取帮助

### 收集诊断信息
```bash
# 创建诊断报告
echo "=== Docker版本 ===" > diagnosis.txt
docker --version >> diagnosis.txt
docker-compose --version >> diagnosis.txt

echo "=== 容器状态 ===" >> diagnosis.txt
docker-compose ps >> diagnosis.txt

echo "=== 系统资源 ===" >> diagnosis.txt
docker stats --no-stream >> diagnosis.txt

echo "=== 最近日志 ===" >> diagnosis.txt
docker-compose logs --tail=50 >> diagnosis.txt
```

### 联系支持
提供以下信息：
1. 操作系统版本
2. Docker Desktop版本
3. 错误信息截图
4. 诊断报告文件
5. 复现步骤

## 预防措施

### 1. 定期维护
```bash
# 每周清理一次
docker system prune -f

# 每月完整清理
docker system prune -a --volumes
```

### 2. 监控设置
```bash
# 设置资源监控
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# 定期检查磁盘使用
docker system df
```

### 3. 备份策略
```bash
# 定期备份配置
cp docker-compose.yml docker-compose.yml.backup

# 备份数据卷
docker run --rm -v joyagent-jdgenie_data:/data -v $(pwd):/backup alpine tar czf /backup/data-$(date +%Y%m%d).tar.gz -C /data .
```
