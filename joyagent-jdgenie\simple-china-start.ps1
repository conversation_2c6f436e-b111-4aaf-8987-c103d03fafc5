# JoyAgent-JDGenie 简单启动脚本 (中国大陆优化)
Write-Host "🚀 JoyAgent-JDGenie 简单启动脚本" -ForegroundColor Blue
Write-Host "=================================="

# 第1步：配置Docker镜像源
Write-Host "第1步：配置Docker镜像源..." -ForegroundColor Yellow

$dockerConfigDir = "$env:USERPROFILE\.docker"
if (-not (Test-Path $dockerConfigDir)) {
    New-Item -ItemType Directory -Path $dockerConfigDir -Force
}

$daemonJson = '{
  "registry-mirrors": [
    "https://docker.m.daocloud.io",
    "https://dockerproxy.com",
    "https://mirror.baidubce.com",
    "https://reg-mirror.qiniu.com"
  ],
  "builder": {
    "gc": {
      "defaultKeepStorage": "20GB",
      "enabled": true
    }
  }
}'

$daemonJson | Out-File "$dockerConfigDir\daemon.json" -Encoding UTF8
Write-Host "✅ Docker镜像源配置完成" -ForegroundColor Green

# 第2步：重启Docker Desktop
Write-Host ""
Write-Host "第2步：重启Docker Desktop..." -ForegroundColor Yellow

Get-Process "Docker Desktop" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 5
Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
Write-Host "✅ Docker Desktop已重启" -ForegroundColor Green

# 第3步：等待Docker启动
Write-Host ""
Write-Host "第3步：等待Docker启动..." -ForegroundColor Yellow

$attempts = 0
$maxAttempts = 30

while ($attempts -lt $maxAttempts) {
    $attempts++
    Write-Host "检查Docker状态... ($attempts/$maxAttempts)" -NoNewline
    
    docker info *>$null 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host " ✅" -ForegroundColor Green
        break
    }
    
    Write-Host " ⏳" -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

if ($attempts -ge $maxAttempts) {
    Write-Host "❌ Docker启动超时" -ForegroundColor Red
    Read-Host "按回车键退出..."
    exit 1
}

# 第4步：测试Docker镜像源
Write-Host ""
Write-Host "第4步：测试Docker镜像源..." -ForegroundColor Yellow

docker pull hello-world *>$null 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Docker镜像源工作正常" -ForegroundColor Green
} else {
    Write-Host "⚠️ Docker镜像源可能较慢，但继续启动" -ForegroundColor Yellow
}

# 第5步：启动JoyAgent-JDGenie
Write-Host ""
Write-Host "第5步：启动JoyAgent-JDGenie..." -ForegroundColor Yellow

docker-compose up -d --build
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 启动命令执行成功" -ForegroundColor Green
} else {
    Write-Host "❌ 启动命令执行失败" -ForegroundColor Red
    Read-Host "按回车键退出..."
    exit 1
}

# 第6步：等待服务启动
Write-Host ""
Write-Host "第6步：等待服务启动..." -ForegroundColor Yellow

$serviceChecks = 0
$maxServiceChecks = 30

while ($serviceChecks -lt $maxServiceChecks) {
    $serviceChecks++
    $readyCount = 0
    
    # 检查前端服务
    try {
        $response = Invoke-WebRequest "http://localhost:3000" -TimeoutSec 2 -UseBasicParsing -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) { $readyCount++ }
    } catch { }
    
    # 检查后端服务
    try {
        $response = Invoke-WebRequest "http://localhost:8080" -TimeoutSec 2 -UseBasicParsing -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) { $readyCount++ }
    } catch { }
    
    # 检查工具服务
    try {
        $response = Invoke-WebRequest "http://localhost:1601" -TimeoutSec 2 -UseBasicParsing -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) { $readyCount++ }
    } catch { }
    
    # 检查MCP客户端
    try {
        $response = Invoke-WebRequest "http://localhost:8188" -TimeoutSec 2 -UseBasicParsing -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) { $readyCount++ }
    } catch { }
    
    Write-Host "服务启动进度: $readyCount/4 ($serviceChecks/$maxServiceChecks)" -ForegroundColor Cyan
    
    if ($readyCount -eq 4) {
        Write-Host "✅ 所有服务已启动！" -ForegroundColor Green
        break
    }
    
    Start-Sleep -Seconds 10
}

# 第7步：显示结果
Write-Host ""
Write-Host "=================================="
Write-Host "🎉 JoyAgent-JDGenie 启动完成！" -ForegroundColor Green
Write-Host "=================================="

Write-Host "🌐 访问地址：" -ForegroundColor Blue
Write-Host "• 主界面: http://localhost:3000" -ForegroundColor Cyan
Write-Host "• 后端API: http://localhost:8080" -ForegroundColor Cyan
Write-Host "• 工具服务: http://localhost:1601" -ForegroundColor Cyan
Write-Host "• MCP客户端: http://localhost:8188" -ForegroundColor Cyan

Write-Host ""
Write-Host "🔧 已配置的LLM服务：" -ForegroundColor Blue
Write-Host "• DeepSeek: ***********************************" -ForegroundColor Cyan
Write-Host "• 火山方舟: bcdd2ded-7352-4668-9fef-ebe31b56177a" -ForegroundColor Cyan
Write-Host "• Serper搜索: 04d74f4080b9379d5875e04bccc3000aa8e7840f" -ForegroundColor Cyan

Write-Host ""
Write-Host "💡 使用提示：" -ForegroundColor Blue
Write-Host "• 已配置国内Docker镜像源，下载更快" -ForegroundColor Cyan
Write-Host "• 如有问题，运行: docker-compose logs -f" -ForegroundColor Cyan

Write-Host "=================================="

$openBrowser = Read-Host "是否打开浏览器访问主界面？(Y/N)"
if ($openBrowser -eq "Y" -or $openBrowser -eq "y") {
    Start-Process "http://localhost:3000"
}

Write-Host "感谢使用JoyAgent-JDGenie！"
Read-Host "按回车键退出..."
