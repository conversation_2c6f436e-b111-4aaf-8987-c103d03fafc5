# WSL和Docker完整配置脚本
Write-Host "🚀 WSL和Docker完整配置脚本" -ForegroundColor Blue
Write-Host "=================================="

# 检查当前目录
$currentDir = Get-Location
Write-Host "📂 当前目录: $currentDir" -ForegroundColor Green

# 第1步：下载WSL内核更新包
Write-Host ""
Write-Host "第1步：下载WSL内核更新包..." -ForegroundColor Yellow
if (-not (Test-Path "wsl_update_x64.msi")) {
    try {
        Write-Host "正在下载WSL内核更新包..."
        Invoke-WebRequest -Uri "https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi" -OutFile "wsl_update_x64.msi"
        Write-Host "✅ 下载完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请手动下载: https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi"
    }
} else {
    Write-Host "✅ WSL更新包已存在" -ForegroundColor Green
}

# 第2步：安装WSL内核更新包
Write-Host ""
Write-Host "第2步：安装WSL内核更新包..." -ForegroundColor Yellow
if (Test-Path "wsl_update_x64.msi") {
    try {
        Start-Process msiexec.exe -Wait -ArgumentList '/I wsl_update_x64.msi /quiet'
        Write-Host "✅ WSL内核更新包安装完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 安装失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 第3步：配置WSL
Write-Host ""
Write-Host "第3步：配置WSL..." -ForegroundColor Yellow
try {
    wsl --set-default-version 2
    Write-Host "✅ WSL默认版本设置为2" -ForegroundColor Green
} catch {
    Write-Host "⚠️ WSL版本设置可能失败" -ForegroundColor Yellow
}

try {
    wsl --update
    Write-Host "✅ WSL更新完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️ WSL更新可能失败" -ForegroundColor Yellow
}

# 第4步：检查并安装Ubuntu
Write-Host ""
Write-Host "第4步：检查并安装Ubuntu..." -ForegroundColor Yellow
$wslList = wsl --list --verbose 2>$null
if ($wslList -match "Ubuntu") {
    Write-Host "✅ Ubuntu已安装" -ForegroundColor Green
} else {
    Write-Host "正在安装Ubuntu..."
    try {
        wsl --install -d Ubuntu --no-launch
        Write-Host "✅ Ubuntu安装完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ Ubuntu安装失败" -ForegroundColor Red
        Write-Host "💡 请手动通过Microsoft Store安装Ubuntu" -ForegroundColor Yellow
        Start-Process "ms-windows-store://pdp/?productid=9PDXGNCFSCZV"
    }
}

# 第5步：重启Docker Desktop
Write-Host ""
Write-Host "第5步：重启Docker Desktop..." -ForegroundColor Yellow
try {
    # 停止Docker Desktop
    Get-Process "Docker Desktop" -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "Docker Desktop已停止"
    
    # 等待3秒
    Start-Sleep -Seconds 3
    
    # 启动Docker Desktop
    Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    Write-Host "✅ Docker Desktop已启动" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Docker Desktop重启可能失败" -ForegroundColor Yellow
}

# 第6步：等待Docker启动
Write-Host ""
Write-Host "第6步：等待Docker启动..." -ForegroundColor Yellow
Write-Host "正在等待Docker Engine启动..."

$maxAttempts = 30
$attempt = 0
$dockerReady = $false

while ($attempt -lt $maxAttempts -and -not $dockerReady) {
    $attempt++
    Write-Host "检查Docker状态... ($attempt/$maxAttempts)" -NoNewline
    
    try {
        docker info *>$null
        if ($LASTEXITCODE -eq 0) {
            $dockerReady = $true
            Write-Host " ✅" -ForegroundColor Green
            break
        }
    } catch {}
    
    Write-Host " ⏳" -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

if ($dockerReady) {
    Write-Host "✅ Docker已就绪！" -ForegroundColor Green
    
    # 第7步：测试Docker
    Write-Host ""
    Write-Host "第7步：测试Docker功能..." -ForegroundColor Yellow
    try {
        docker run --rm hello-world *>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Docker测试成功！" -ForegroundColor Green
        } else {
            Write-Host "❌ Docker测试失败" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Docker测试失败" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Docker启动超时" -ForegroundColor Red
    Write-Host "💡 请手动检查Docker Desktop是否正常启动" -ForegroundColor Yellow
}

# 完成
Write-Host ""
Write-Host "=================================="
Write-Host "🎉 配置完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📂 项目目录: d:\JDAGENT\joyagent-jdgenie" -ForegroundColor Blue
Write-Host "🚀 下一步: 运行 docker-start.sh" -ForegroundColor Blue
Write-Host "🌐 访问地址: http://localhost:3000" -ForegroundColor Blue
Write-Host "=================================="

Read-Host "按回车键继续..."
