<!DOCTYPE html><html><head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge,chrome=1">
    <base href="/mirror/">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="Cache-Control" content="public, max-age=31536000">
    <meta name="keywords" content="镜像,镜像站,软件开发,编译构建,maven,npm,pypi,nuget,php,rubygem,sbt,操作系统">
    <meta name="description" content="华为云DevCloud团队提供的全类型镜像站服务，提供主流开发语言组件、操作系统、常用工具和库等镜像，极速下载，全站CDN，官方合作。">
    <link rel="icon" type="image/x-icon" href="//devcloud-res.hc-cdn.com/MirrorPortal-CDN/2025.4.0/hws/favicon.ico">

    <script>
      const $favicon = document.querySelector('link[rel="icon"]');
      let locationHost = window.location.host;
      if (
        (locationHost.indexOf('xfusion.com') > -1 ||
          locationHost.indexOf('beta2.huawei.com') > -1 ||
          locationHost.indexOf('heds-beta2.huawei.com') > -1) &&
        $favicon
      ) {
        $favicon.href = null;
      }
    </script>

    <script>
      window.devEnvName = 'hws';
      window.header_url = 'is_global';
      window.app_cookie_prefix = 'cftk';
      window.oversea = '';
      window.platform = 'devcloud';
      window.appName = 'mirror';
      window.globalVars = JSON.parse('{"Portal_devEnv_name":" hws","__POWERED_BY_DRAGONFLY__":true}');
    </script>

    <script>
      window.devEnvName = 'Portal_devEnv_name';

      window.inhuaweiEnable = String(window.platform === 'clouddragon');
      localStorage.setItem('inhuaweiEnable', window.inhuaweiEnable);

      localStorage.setItem('newVersion', 'true');
      window.isNewVisionFeedback = localStorage.getItem('isNewVisionFeedback');
      if (window.isNewVisionFeedback === 'false' || window.isNewVisionFeedback == null) {
        localStorage.setItem('newVersion', 'true');
      }

      window.isNewVersion = localStorage.getItem('newVersion');
    </script>
  <style>@charset "UTF-8";html{line-height:1.15;-webkit-text-size-adjust:100%}body{margin:0}body{margin:0;padding:0;color:#252b3a;color:var(--devui-text,#252b3a);font-size:12px;font-size:var(--devui-font-size,12px);font-family:Helvetica,Arial,PingFangSC-Regular,Hiragino Sans GB,Microsoft YaHei,\5fae\8f6f\96c5\9ed1,HarmonyOS Sans SC,HarmonyOS Sans,Microsoft JhengHei;line-height:1.5;line-height:var(--devui-line-height-base,1.5)}*,:after,:before{box-sizing:border-box}body>* ::-webkit-scrollbar{width:8px;height:8px}@-moz-document url-prefix(){body *{scrollbar-width:thin}}body>* ::-webkit-scrollbar-track{background-color:transparent}body ::-moz-scrollbar-track{background-color:transparent}body>* ::-webkit-scrollbar-thumb{border-radius:8px;background-color:#adb0b8;background-color:var(--devui-line,#adb0b8)}body>* ::-webkit-scrollbar-thumb:hover{background-color:#8a8e99;background-color:var(--devui-placeholder,#8a8e99)}body>* ::-webkit-scrollbar-corner{background-color:transparent}</style><link rel="stylesheet" href="//devcloud-res.hc-cdn.com/MirrorPortal-CDN/2025.4.0/hws/styles.361e25ccf7ac355b.css" media="print" onload="this.media='all'"><noscript><link rel="stylesheet" href="//devcloud-res.hc-cdn.com/MirrorPortal-CDN/2025.4.0/hws/styles.361e25ccf7ac355b.css"></noscript><script>
window['cftk_cookie_key_cf2']='devclouddevuibjtcftk';
window['headerapp_resource']={
  "dir": "//devcloud-res.hc-cdn.com/HeaderAppCDN/25.5.5/hws/",
  "version": "25.5.5",
  "config": {
    "isOversea": "",
    "isOverseaPackage": "",
    "purchaseDetailUrl": "",
    "feedbackScripts": "https://res.hc-cdn.com/NPS-component/1.0.5/hws/nps-feedback.js",
    "professionalServiceUrl": "https://console.huaweicloud.com/professionalService",
    "aiAssistantUrl": "https://console.huaweicloud.com/smartadvisor",
    "feedbackUrl": "https://bbs.huaweicloud.com/suggestion/new",
    "DevUIAdminServiceIam5Regions": "cn-north-1,cn-north-4,cn-southwest-2,cn-east-2,cn-south-1,cn-east-3",
    "DevCloudConsoleIam5Regions": "",
    "ProjectServiceIam5Regions": "cn-north-1,cn-north-4,cn-southwest-2,cn-east-2,cn-south-1,cn-east-3",
    "workspaceXWhiteList": "afc4784e64d146549764787876c60856,aae03ca0d6814951980945b6eeca56cd,f65db0e9f7a04bbf85cb2b80bd4f84c6,40593b93eed64b62bab232f7877c08d0,91e65278dfc84e1eb657d26209edd54b,09cad66b4a80f4690f88c019cc4a6740",
    "envName": "ENV_NAME"
  }
};
window['service_cf3_config']={"nps":{"surveyId":"hwcloudbusurvey_key_fbd25bdbdb87","contactId":"global","serviceName":"CodeArts-Mirror","serviceId":"CS12020808","script":"https://res.hc-cdn.com/NPS-component/1.0.5/hws/nps-feedback.js"},"homeUrl":"https://www.huaweicloud.com/","furion_cdn_url":"https://devcloud-res.hc-cdn.com/FurionSdkCDN/1.0.22/furion-cdn.min.js","baseUrl":"https://mirrors.huaweicloud.com","baseUrlCDN":"https://repo.huaweicloud.com","isSupportExternalLink":true,"envInfoId":"devcloud","devcloud_url":"https://devcloud.huaweicloud.com","register_url":"https://reg.huaweicloud.com/registerui/cn/register.html?locale=zh-cn","private_url":"https://devcloud.cn-north-4.huaweicloud.com/cloudartifact/repository/maven","furion_app_id":"5B69D9AB5FF940F685E4E36C0487350D","guide":{"rubygemsDownloadLink":"https://rubygems.org/pages/download?locale=zh-CN","jenkinsUpdatesCenterJSONPath":"/jenkins/updates/current/update-center.json","rubyPath":"/ruby/ruby/","rubygemsBundlerConfigLink":"https://bundler.io/v1.16/man/bundle-config.1.html#MIRRORS-OF-GEM-SOURCES"}};
</script>
</head>

  <body class="prerender-auto-height">
    <!--insertHeaderScriptHere-->
    <!--FurionScriptBegin-->
		<script>if(window.service_cf3_config.furion_app_id &&
    window.service_cf3_config.furion_cdn_url){!function(x,n){window[n]=window[n]||{};window[n].config={"appId":window.service_cf3_config.furion_app_id,"setting":"perf,uba,jsTrack,api,longtask,rtti"};var o=document.createElement('script');o.src=x,o.async=!0;
  var d=document.body.firstChild;document.body.insertBefore(o,d);}(window.service_cf3_config.furion_cdn_url,'__fr');}</script>
<!--FurionScriptEnd-->

   <!--insertOnloadScriptHere-->
   <script>
    function isExecuteInsertInto() {
      return (
        !window?.service_cf3_config?.envInfoId ||
        window?.service_cf3_config?.envInfoId !== 'codearts' ||
        window?.service_cf3_config?.envInfoId !== 'heds' ||
        window?.service_cf3_config?.envInfoId !== 'hedsTest'
      );
    }

    if (isExecuteInsertInto() && !location.host.includes('dev.huawei')) {
      const uemLoadingRate = window.service_cf3_config?.uem_loading_rate ?? 100;
      const random = Math.floor(Math.random() * 100) + 1;
      if (random <= uemLoadingRate) {
        window.uemBetaAppId = 'a2f87770efd837b54f4c50ddcfe8c3cc';
        window.uemProdAppId = 'a419e557b46b7fb81b7681a6a250909d';
        const uemUrl = '//devcloud-res.hc-cdn.com/MirrorPortal-CDN/2025.4.0/hws/UEM_V4.js';
        let script = document.createElement('script');
        script.src = uemUrl;
        script.async = true;
        let firstChild = document.body.firstChild;
        document.body.insertBefore(script, firstChild);
      }
    }
  </script>
    <app-root devui-devcloud-version="14.6.9" devui-assets-version="4.10.4" devcloud-console-version="7.3.14" devui-material-cli-version="1.72.0" ng-devui-version="14.117.0"></app-root>
  <script src="//devcloud-res.hc-cdn.com/MirrorPortal-CDN/2025.4.0/hws/runtime.715e005554d2bb8c.js" type="module"></script><script src="//devcloud-res.hc-cdn.com/MirrorPortal-CDN/2025.4.0/hws/polyfills.c2ecff71e147184d.js" type="module"></script><script src="//devcloud-res.hc-cdn.com/MirrorPortal-CDN/2025.4.0/hws/main.0ac9f87fec625258.js" type="module"></script>

</body></html>