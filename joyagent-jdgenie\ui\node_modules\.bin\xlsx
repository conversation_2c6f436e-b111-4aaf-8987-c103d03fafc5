#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/xlsx@0.18.5/node_modules/xlsx/bin/node_modules:/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/xlsx@0.18.5/node_modules/xlsx/node_modules:/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/xlsx@0.18.5/node_modules:/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/xlsx@0.18.5/node_modules/xlsx/bin/node_modules:/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/xlsx@0.18.5/node_modules/xlsx/node_modules:/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/xlsx@0.18.5/node_modules:/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../xlsx/bin/xlsx.njs" "$@"
else
  exec node  "$basedir/../xlsx/bin/xlsx.njs" "$@"
fi
