#!/bin/bash

echo "⏳ 等待Docker Engine完全启动..."
echo "=================================="

max_attempts=60
attempt=0
docker_ready=false

while [ $attempt -lt $max_attempts ] && [ "$docker_ready" = false ]; do
    attempt=$((attempt + 1))
    echo -ne "\r检查Docker状态... ($attempt/$max_attempts) "
    
    # 测试Docker是否响应
    if timeout 5 docker info > /dev/null 2>&1; then
        docker_ready=true
        echo ""
        echo "✅ Docker Engine已就绪！"
        break
    fi
    
    # 显示进度点
    case $((attempt % 4)) in
        1) echo -ne "⠋" ;;
        2) echo -ne "⠙" ;;
        3) echo -ne "⠹" ;;
        0) echo -ne "⠸" ;;
    esac
    
    sleep 2
done

echo ""

if [ "$docker_ready" = true ]; then
    echo "=================================="
    echo "🎉 Docker已准备就绪！"
    echo ""
    echo "Docker信息："
    docker info | head -5
    echo ""
    echo "现在可以开始构建JoyAgent-JDGenie了"
    echo "=================================="
    exit 0
else
    echo "=================================="
    echo "❌ Docker启动超时"
    echo ""
    echo "💡 建议操作："
    echo "1. 检查Docker Desktop是否完全启动"
    echo "2. 重启Docker Desktop"
    echo "3. 检查系统资源是否充足"
    echo "4. 重启计算机"
    echo "=================================="
    exit 1
fi
