# JoyAgent-JDGenie 无Docker启动脚本
Write-Host "🚀 JoyAgent-JDGenie 无Docker启动" -ForegroundColor Blue
Write-Host "=================================="

# 检查必要工具
Write-Host "检查必要工具..." -ForegroundColor Yellow

$tools = @{
    "Node.js" = { node --version }
    "npm" = { npm --version }
    "Java" = { java -version }
    "Maven" = { mvn --version }
    "Python" = { python --version }
    "pip" = { pip --version }
}

$missingTools = @()

foreach ($tool in $tools.Keys) {
    Write-Host "检查 $tool..." -NoNewline
    try {
        & $tools[$tool] *>$null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host " ✅" -ForegroundColor Green
        } else {
            Write-Host " ❌" -ForegroundColor Red
            $missingTools += $tool
        }
    } catch {
        Write-Host " ❌" -ForegroundColor Red
        $missingTools += $tool
    }
}

if ($missingTools.Count -gt 0) {
    Write-Host "`n❌ 缺少必要工具: $($missingTools -join ', ')" -ForegroundColor Red
    Write-Host "请先安装这些工具后再运行此脚本" -ForegroundColor Yellow
    Read-Host "按回车键退出..."
    exit 1
}

# 检查项目结构
Write-Host "`n检查项目结构..." -ForegroundColor Yellow

$components = @(
    @{ Name = "前端"; Path = "frontend"; Required = $true },
    @{ Name = "后端"; Path = "backend"; Required = $true },
    @{ Name = "工具服务"; Path = "tools-service"; Required = $true },
    @{ Name = "MCP客户端"; Path = "mcp-client"; Required = $false }
)

$missingComponents = @()

foreach ($component in $components) {
    Write-Host "检查 $($component.Name)..." -NoNewline
    if (Test-Path $component.Path) {
        Write-Host " ✅" -ForegroundColor Green
    } else {
        Write-Host " ❌" -ForegroundColor Red
        if ($component.Required) {
            $missingComponents += $component.Name
        }
    }
}

if ($missingComponents.Count -gt 0) {
    Write-Host "`n❌ 缺少必要组件: $($missingComponents -join ', ')" -ForegroundColor Red
    Write-Host "请确保项目结构完整" -ForegroundColor Yellow
    Read-Host "按回车键退出..."
    exit 1
}

# 创建启动函数
function Start-Frontend {
    Write-Host "`n启动前端服务..." -ForegroundColor Yellow
    
    Set-Location frontend
    
    # 检查node_modules
    if (-not (Test-Path "node_modules")) {
        Write-Host "安装前端依赖..." -ForegroundColor Cyan
        npm install
    }
    
    # 启动前端
    Start-Process powershell -ArgumentList "-Command", "npm run dev; Read-Host 'Press Enter to exit...'" -WindowStyle Normal -WorkingDirectory (Get-Location)
    
    Set-Location ..
    Write-Host "✅ 前端服务启动中 (http://localhost:3000)" -ForegroundColor Green
}

function Start-Backend {
    Write-Host "`n启动后端服务..." -ForegroundColor Yellow
    
    Set-Location backend
    
    # 检查是否需要构建
    if (-not (Test-Path "target\joyagent-backend.jar")) {
        Write-Host "构建后端服务..." -ForegroundColor Cyan
        mvn clean package -DskipTests
    }
    
    # 启动后端
    Start-Process powershell -ArgumentList "-Command", "java -jar target\joyagent-backend.jar; Read-Host 'Press Enter to exit...'" -WindowStyle Normal -WorkingDirectory (Get-Location)
    
    Set-Location ..
    Write-Host "✅ 后端服务启动中 (http://localhost:8080)" -ForegroundColor Green
}

function Start-ToolsService {
    Write-Host "`n启动工具服务..." -ForegroundColor Yellow
    
    Set-Location tools-service
    
    # 检查虚拟环境
    if (-not (Test-Path "venv")) {
        Write-Host "创建Python虚拟环境..." -ForegroundColor Cyan
        python -m venv venv
    }
    
    # 激活虚拟环境并安装依赖
    $activateScript = "venv\Scripts\activate"
    if (-not (Test-Path $activateScript)) {
        Write-Host "❌ 虚拟环境激活脚本未找到" -ForegroundColor Red
        Set-Location ..
        return
    }
    
    # 启动工具服务
    Start-Process powershell -ArgumentList "-Command", "& '$activateScript'; pip install -r requirements.txt; python app.py; Read-Host 'Press Enter to exit...'" -WindowStyle Normal -WorkingDirectory (Get-Location)
    
    Set-Location ..
    Write-Host "✅ 工具服务启动中 (http://localhost:1601)" -ForegroundColor Green
}

function Start-McpClient {
    if (-not (Test-Path "mcp-client")) {
        Write-Host "`n⚠️ MCP客户端目录不存在，跳过启动" -ForegroundColor Yellow
        return
    }
    
    Write-Host "`n启动MCP客户端..." -ForegroundColor Yellow
    
    Set-Location mcp-client
    
    # 检查node_modules
    if (-not (Test-Path "node_modules")) {
        Write-Host "安装MCP客户端依赖..." -ForegroundColor Cyan
        npm install
    }
    
    # 启动MCP客户端
    Start-Process powershell -ArgumentList "-Command", "npm start; Read-Host 'Press Enter to exit...'" -WindowStyle Normal -WorkingDirectory (Get-Location)
    
    Set-Location ..
    Write-Host "✅ MCP客户端启动中 (http://localhost:8188)" -ForegroundColor Green
}

# 启动所有服务
Write-Host "`n启动所有服务..." -ForegroundColor Yellow

# 询问用户要启动哪些服务
Write-Host "`n请选择要启动的服务:" -ForegroundColor Cyan
Write-Host "1. 所有服务" -ForegroundColor White
Write-Host "2. 仅前端" -ForegroundColor White
Write-Host "3. 仅后端" -ForegroundColor White
Write-Host "4. 仅工具服务" -ForegroundColor White
Write-Host "5. 自定义选择" -ForegroundColor White

$choice = Read-Host "请输入选项 (1-5)"

switch ($choice) {
    "1" {
        Start-Frontend
        Start-Backend
        Start-ToolsService
        Start-McpClient
    }
    "2" {
        Start-Frontend
    }
    "3" {
        Start-Backend
    }
    "4" {
        Start-ToolsService
    }
    "5" {
        $startFrontend = Read-Host "启动前端? (Y/N)"
        if ($startFrontend -eq "Y" -or $startFrontend -eq "y") {
            Start-Frontend
        }
        
        $startBackend = Read-Host "启动后端? (Y/N)"
        if ($startBackend -eq "Y" -or $startBackend -eq "y") {
            Start-Backend
        }
        
        $startTools = Read-Host "启动工具服务? (Y/N)"
        if ($startTools -eq "Y" -or $startTools -eq "y") {
            Start-ToolsService
        }
        
        $startMcp = Read-Host "启动MCP客户端? (Y/N)"
        if ($startMcp -eq "Y" -or $startMcp -eq "y") {
            Start-McpClient
        }
    }
    default {
        Write-Host "❌ 无效选项，启动所有服务" -ForegroundColor Red
        Start-Frontend
        Start-Backend
        Start-ToolsService
        Start-McpClient
    }
}

# 显示总结信息
Write-Host "`n=================================="
Write-Host "🎉 JoyAgent-JDGenie 启动完成！" -ForegroundColor Green
Write-Host "=================================="

Write-Host "`n🌐 访问地址：" -ForegroundColor Blue
Write-Host "• 前端: http://localhost:3000" -ForegroundColor Cyan
Write-Host "• 后端: http://localhost:8080" -ForegroundColor Cyan
Write-Host "• 工具服务: http://localhost:1601" -ForegroundColor Cyan
Write-Host "• MCP客户端: http://localhost:8188" -ForegroundColor Cyan

Write-Host "`n💡 注意事项：" -ForegroundColor Blue
Write-Host "• 各服务在独立的PowerShell窗口中运行" -ForegroundColor Cyan
Write-Host "• 关闭窗口即可停止对应服务" -ForegroundColor Cyan
Write-Host "• 首次启动可能需要较长时间" -ForegroundColor Cyan

$openBrowser = Read-Host "`n是否打开浏览器访问前端? (Y/N)"
if ($openBrowser -eq "Y" -or $openBrowser -eq "y") {
    Start-Process "http://localhost:3000"
}

Write-Host "`n感谢使用JoyAgent-JDGenie！" -ForegroundColor Green
Read-Host "按回车键退出..."
