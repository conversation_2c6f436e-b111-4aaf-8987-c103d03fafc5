# 中国大陆优化启动脚本 (修复版)
Write-Host "🇨🇳 JoyAgent-JDGenie 中国大陆优化启动脚本" -ForegroundColor Blue
Write-Host "=================================="

# 第1步：配置Docker镜像源
Write-Host "第1步：配置Docker镜像源..." -ForegroundColor Yellow

$dockerConfigDir = "$env:USERPROFILE\.docker"
if (-not (Test-Path $dockerConfigDir)) {
    New-Item -ItemType Directory -Path $dockerConfigDir -Force
}

$daemonConfig = @{
    "registry-mirrors" = @(
        "https://docker.m.daocloud.io",
        "https://dockerproxy.com",
        "https://mirror.baidubce.com",
        "https://reg-mirror.qiniu.com"
    )
    "insecure-registries" = @()
    "debug" = $false
    "experimental" = $false
    "builder" = @{
        "gc" = @{
            "defaultKeepStorage" = "20GB"
            "enabled" = $true
        }
    }
}

$daemonConfigPath = "$dockerConfigDir\daemon.json"
$daemonConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $daemonConfigPath -Encoding UTF8
Write-Host "✅ Docker镜像源配置完成" -ForegroundColor Green

# 第2步：重启Docker Desktop
Write-Host ""
Write-Host "第2步：重启Docker Desktop..." -ForegroundColor Yellow

try {
    Get-Process "Docker Desktop" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 5
    Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    Write-Host "✅ Docker Desktop已重启" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Desktop重启失败" -ForegroundColor Red
}

# 第3步：等待Docker启动
Write-Host ""
Write-Host "第3步：等待Docker启动..." -ForegroundColor Yellow

$maxAttempts = 30
$attempt = 0
$dockerReady = $false

while ($attempt -lt $maxAttempts -and -not $dockerReady) {
    $attempt++
    Write-Host "检查Docker状态... ($attempt/$maxAttempts)" -NoNewline
    
    try {
        docker info *>$null 2>&1
        if ($LASTEXITCODE -eq 0) {
            $dockerReady = $true
            Write-Host " ✅" -ForegroundColor Green
            break
        }
    } catch {
        # 忽略错误
    }
    
    Write-Host " ⏳" -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

if (-not $dockerReady) {
    Write-Host "❌ Docker启动超时" -ForegroundColor Red
    Read-Host "按回车键退出..."
    exit 1
}

# 第4步：测试Docker镜像源
Write-Host ""
Write-Host "第4步：测试Docker镜像源..." -ForegroundColor Yellow

try {
    Write-Host "拉取测试镜像: hello-world"
    docker pull hello-world
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker镜像源工作正常" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Docker镜像拉取可能较慢，但配置已完成" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Docker镜像测试失败，但配置已完成" -ForegroundColor Yellow
}

# 第5步：启动JoyAgent-JDGenie
Write-Host ""
Write-Host "第5步：启动JoyAgent-JDGenie..." -ForegroundColor Yellow

try {
    if (Test-Path "docker-start.sh") {
        Write-Host "使用docker-start.sh启动..."
        bash docker-start.sh
    } else {
        Write-Host "使用docker-compose启动..."
        docker-compose up -d --build
    }
    
    Write-Host "✅ JoyAgent-JDGenie启动命令已执行" -ForegroundColor Green
} catch {
    Write-Host "❌ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 第6步：等待服务启动
Write-Host ""
Write-Host "第6步：等待服务启动..." -ForegroundColor Yellow

$services = @(
    @{Name="前端服务"; Url="http://localhost:3000"},
    @{Name="后端服务"; Url="http://localhost:8080"},
    @{Name="工具服务"; Url="http://localhost:1601"},
    @{Name="MCP客户端"; Url="http://localhost:8188"}
)

$maxWait = 300
$startTime = Get-Date

while ((Get-Date) -lt $startTime.AddSeconds($maxWait)) {
    $readyServices = 0
    
    foreach ($service in $services) {
        try {
            $response = Invoke-WebRequest -Uri $service.Url -TimeoutSec 2 -UseBasicParsing -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                $readyServices++
            }
        } catch {
            # 忽略错误
        }
    }
    
    $elapsed = [math]::Round(((Get-Date) - $startTime).TotalSeconds)
    Write-Host "服务启动进度: $readyServices/$($services.Count) ($elapsed 秒)" -ForegroundColor Cyan
    
    if ($readyServices -eq $services.Count) {
        Write-Host "✅ 所有服务已启动！" -ForegroundColor Green
        break
    }
    
    Start-Sleep -Seconds 10
}

# 第7步：显示最终状态
Write-Host ""
Write-Host "=================================="
Write-Host "🎉 JoyAgent-JDGenie 启动完成！" -ForegroundColor Green
Write-Host "=================================="

Write-Host "📊 服务状态检查：" -ForegroundColor Blue
foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri $service.Url -TimeoutSec 5 -UseBasicParsing -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($service.Name): 运行正常" -ForegroundColor Green
        } else {
            Write-Host "❌ $($service.Name): 状态异常" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $($service.Name): 无法访问" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🌐 访问地址：" -ForegroundColor Blue
Write-Host "• 主界面: http://localhost:3000" -ForegroundColor Cyan
Write-Host "• 后端API: http://localhost:8080" -ForegroundColor Cyan
Write-Host "• 工具服务: http://localhost:1601" -ForegroundColor Cyan
Write-Host "• MCP客户端: http://localhost:8188" -ForegroundColor Cyan

Write-Host ""
Write-Host "🔧 已配置的LLM服务：" -ForegroundColor Blue
Write-Host "• DeepSeek: ***********************************" -ForegroundColor Cyan
Write-Host "• 火山方舟: bcdd2ded-7352-4668-9fef-ebe31b56177a" -ForegroundColor Cyan
Write-Host "• Serper搜索: 04d74f4080b9379d5875e04bccc3000aa8e7840f" -ForegroundColor Cyan

Write-Host ""
Write-Host "💡 使用提示：" -ForegroundColor Blue
Write-Host "• 首次启动可能需要较长时间下载镜像" -ForegroundColor Cyan
Write-Host "• 已配置国内镜像源，下载速度更快" -ForegroundColor Cyan
Write-Host "• 如有问题，请查看: docker-compose logs -f" -ForegroundColor Cyan

Write-Host "=================================="

# 询问是否打开浏览器
$choice = Read-Host "是否打开浏览器访问主界面？(Y/N)"
if ($choice -eq "Y" -or $choice -eq "y") {
    Start-Process "http://localhost:3000"
}

Read-Host "按回车键退出..."
