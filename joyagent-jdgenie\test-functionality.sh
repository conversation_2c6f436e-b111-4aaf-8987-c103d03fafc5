#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🧪 JoyAgent-JDGenie 功能测试${NC}"
echo "=================================="

# 测试1: 前端页面加载
echo -e "${BLUE}测试1: 前端页面加载${NC}"
if curl -s http://localhost:3000 | grep -q "JoyAgent\|Genie\|React"; then
    echo -e "${GREEN}✅ 前端页面加载正常${NC}"
else
    echo -e "${RED}❌ 前端页面加载失败${NC}"
fi

# 测试2: 后端API健康检查
echo -e "${BLUE}测试2: 后端API健康检查${NC}"
health_response=$(curl -s http://localhost:8080/web/health)
if echo "$health_response" | grep -q "UP\|OK\|healthy"; then
    echo -e "${GREEN}✅ 后端API健康检查通过${NC}"
    echo "响应: $health_response"
else
    echo -e "${RED}❌ 后端API健康检查失败${NC}"
    echo "响应: $health_response"
fi

# 测试3: 工具服务API
echo -e "${BLUE}测试3: 工具服务API${NC}"
tools_response=$(curl -s http://localhost:1601/docs)
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 工具服务API可访问${NC}"
else
    echo -e "${RED}❌ 工具服务API不可访问${NC}"
fi

# 测试4: LLM配置验证
echo -e "${BLUE}测试4: LLM配置验证${NC}"
# 检查环境变量是否正确设置
if docker-compose exec -T genie-app env | grep -q "OPENAI_API_KEY=sk-1d701fa867da47ebb2e1fd8d141795cd"; then
    echo -e "${GREEN}✅ DeepSeek API配置正确${NC}"
else
    echo -e "${RED}❌ DeepSeek API配置异常${NC}"
fi

# 测试5: 搜索功能配置
echo -e "${BLUE}测试5: 搜索功能配置${NC}"
if docker-compose exec -T genie-app env | grep -q "SERPER_SEARCH_API_KEY=04d74f4080b9379d5875e04bccc3000aa8e7840f"; then
    echo -e "${GREEN}✅ 搜索功能配置正确${NC}"
else
    echo -e "${RED}❌ 搜索功能配置异常${NC}"
fi

# 测试6: 数据持久化
echo -e "${BLUE}测试6: 数据持久化检查${NC}"
if docker volume ls | grep -q "joyagent-jdgenie"; then
    echo -e "${GREEN}✅ 数据卷已创建${NC}"
else
    echo -e "${YELLOW}⚠️ 未发现数据卷，数据可能不会持久化${NC}"
fi

echo ""
echo "=================================="
echo -e "${BLUE}📋 测试总结${NC}"
echo "请访问 http://localhost:3000 开始使用JoyAgent-JDGenie"
echo ""
echo -e "${YELLOW}💡 使用建议：${NC}"
echo "1. 首次使用建议先测试简单的对话功能"
echo "2. 然后尝试使用搜索功能"
echo "3. 最后测试复杂的多智能体协作任务"
echo "=================================="
