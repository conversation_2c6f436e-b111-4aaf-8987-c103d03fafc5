#!/bin/bash

echo "🐳 等待Docker Desktop启动..."
echo "请确保Docker Desktop已经启动并运行"
echo "=================================="

max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    attempt=$((attempt + 1))
    echo -ne "\r检查Docker状态... ($attempt/$max_attempts)"
    
    if docker info > /dev/null 2>&1; then
        echo ""
        echo "✅ Docker Desktop已启动并运行正常！"
        echo "=================================="
        docker info | head -5
        echo "=================================="
        exit 0
    fi
    
    sleep 2
done

echo ""
echo "❌ Docker Desktop启动超时"
echo "请手动启动Docker Desktop，然后重新运行此脚本"
exit 1
