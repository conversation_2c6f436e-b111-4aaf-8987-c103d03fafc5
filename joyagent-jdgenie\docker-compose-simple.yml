version: '3.8'

services:
  genie-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: joyagent-jdgenie-simple
    ports:
      - "3000:3000"
      - "8080:8080"
      - "1601:1601"
      - "8188:8188"
    environment:
      - OPENAI_API_KEY=sk-1d701fa867da47ebb2e1fd8d141795cd
      - OPENAI_BASE_URL=https://api.deepseek.com/v1
      - DEFAULT_MODEL=deepseek/deepseek-chat
      - SERPER_SEARCH_API_KEY=04d74f4080b9379d5875e04bccc3000aa8e7840f
    restart: unless-stopped
