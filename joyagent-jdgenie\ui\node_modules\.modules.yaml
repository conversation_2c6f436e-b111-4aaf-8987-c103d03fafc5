hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ant-design/colors@8.0.0':
    '@ant-design/colors': private
  '@ant-design/cssinjs-utils@1.1.3(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@ant-design/cssinjs-utils': private
  '@ant-design/cssinjs@1.24.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@ant-design/cssinjs': private
  '@ant-design/fast-color@2.0.6':
    '@ant-design/fast-color': private
  '@ant-design/icons-svg@4.4.2':
    '@ant-design/icons-svg': private
  '@ant-design/react-slick@1.1.2(react@19.1.1)':
    '@ant-design/react-slick': private
  '@antfu/install-pkg@1.1.0':
    '@antfu/install-pkg': private
  '@antfu/utils@8.1.1':
    '@antfu/utils': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.28.2':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.28.2':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@braintree/sanitize-url@7.1.1':
    '@braintree/sanitize-url': private
  '@chevrotain/cst-dts-gen@11.0.3':
    '@chevrotain/cst-dts-gen': private
  '@chevrotain/gast@11.0.3':
    '@chevrotain/gast': private
  '@chevrotain/regexp-to-ast@11.0.3':
    '@chevrotain/regexp-to-ast': private
  '@chevrotain/types@11.0.3':
    '@chevrotain/types': private
  '@chevrotain/utils@11.0.3':
    '@chevrotain/utils': private
  '@emotion/hash@0.8.0':
    '@emotion/hash': private
  '@emotion/unitless@0.7.5':
    '@emotion/unitless': private
  '@esbuild/aix-ppc64@0.25.8':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.8':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.8':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.8':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.8':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.8':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.8':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.8':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.8':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.8':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.8':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.8':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.8':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.8':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.8':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.8':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.8':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.8':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.8':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.8':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.8':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.8':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.8':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.8':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.8':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.8':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.32.0(jiti@2.5.1))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.1':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.4':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@iconify/types@2.0.0':
    '@iconify/types': private
  '@iconify/utils@2.3.0':
    '@iconify/utils': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@mermaid-js/parser@0.6.2':
    '@mermaid-js/parser': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgr/core@0.2.9':
    '@pkgr/core': private
  '@rc-component/async-validator@5.0.4':
    '@rc-component/async-validator': private
  '@rc-component/color-picker@2.0.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@rc-component/color-picker': private
  '@rc-component/context@1.4.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@rc-component/context': private
  '@rc-component/mini-decimal@1.1.0':
    '@rc-component/mini-decimal': private
  '@rc-component/mutate-observer@1.1.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@rc-component/mutate-observer': private
  '@rc-component/portal@1.1.2(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@rc-component/portal': private
  '@rc-component/qrcode@1.0.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@rc-component/qrcode': private
  '@rc-component/tour@1.15.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@rc-component/tour': private
  '@rc-component/trigger@2.3.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@rc-component/trigger': private
  '@rc-component/util@1.2.2(react-dom@19.1.1(react@19.1.1))(react@19.1.1)':
    '@rc-component/util': private
  '@rolldown/pluginutils@1.0.0-beta.27':
    '@rolldown/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.46.2':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.46.2':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.46.2':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.46.2':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.46.2':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.46.2':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.46.2':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.46.2':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.46.2':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.46.2':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.46.2':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-ppc64-gnu@4.46.2':
    '@rollup/rollup-linux-ppc64-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.46.2':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.46.2':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.46.2':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.46.2':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.46.2':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.46.2':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.46.2':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.46.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@tailwindcss/node@4.1.11':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.11':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.11':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.11':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.11':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.11':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.11':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.11':
    '@tailwindcss/oxide': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-axis@3.0.6':
    '@types/d3-axis': private
  '@types/d3-brush@3.0.6':
    '@types/d3-brush': private
  '@types/d3-chord@3.0.6':
    '@types/d3-chord': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-contour@3.0.6':
    '@types/d3-contour': private
  '@types/d3-delaunay@6.0.4':
    '@types/d3-delaunay': private
  '@types/d3-dispatch@3.0.7':
    '@types/d3-dispatch': private
  '@types/d3-drag@3.0.7':
    '@types/d3-drag': private
  '@types/d3-dsv@3.0.7':
    '@types/d3-dsv': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-fetch@3.0.7':
    '@types/d3-fetch': private
  '@types/d3-force@3.0.10':
    '@types/d3-force': private
  '@types/d3-format@3.0.4':
    '@types/d3-format': private
  '@types/d3-geo@3.1.0':
    '@types/d3-geo': private
  '@types/d3-hierarchy@3.1.7':
    '@types/d3-hierarchy': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-polygon@3.0.2':
    '@types/d3-polygon': private
  '@types/d3-quadtree@3.0.6':
    '@types/d3-quadtree': private
  '@types/d3-random@3.0.3':
    '@types/d3-random': private
  '@types/d3-scale-chromatic@3.1.0':
    '@types/d3-scale-chromatic': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-selection@3.0.11':
    '@types/d3-selection': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time-format@4.0.3':
    '@types/d3-time-format': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/d3-transition@3.0.9':
    '@types/d3-transition': private
  '@types/d3-zoom@3.0.8':
    '@types/d3-zoom': private
  '@types/d3@7.4.3':
    '@types/d3': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/geojson@7946.0.16':
    '@types/geojson': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/history@4.7.11':
    '@types/history': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/react-router@5.1.20':
    '@types/react-router': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@typescript-eslint/project-service@8.38.0(typescript@5.7.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.38.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.38.0(typescript@5.7.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.38.0(eslint@9.32.0(jiti@2.5.1))(typescript@5.7.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.38.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.38.0(typescript@5.7.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.38.0(eslint@9.32.0(jiti@2.5.1))(typescript@5.7.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.38.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/promise-all-settled@1.1.2':
    '@ungap/promise-all-settled': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  adler-32@1.3.1:
    adler-32: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv@6.12.6:
    ajv: private
  ansi-colors@4.1.1:
    ansi-colors: private
  ansi-regex@3.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  append-transform@2.0.0:
    append-transform: private
  archy@1.0.0:
    archy: private
  argparse@2.0.1:
    argparse: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.9:
    array-includes: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  assertion-error@1.1.0:
    assertion-error: private
  async-function@1.0.0:
    async-function: private
  asynckit@0.4.0:
    asynckit: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  babel-runtime@6.26.0:
    babel-runtime: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browser-stdout@1.3.1:
    browser-stdout: private
  browserslist@4.25.1:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  bundle-require@4.2.1(esbuild@0.25.8):
    bundle-require: private
  caching-transform@4.0.0:
    caching-transform: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase@5.3.1:
    camelcase: private
  caniuse-lite@1.0.30001731:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  cfb@1.2.2:
    cfb: private
  chai@4.5.0:
    chai: private
  chalk@4.1.2:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@1.1.4:
    character-entities-legacy: private
  character-entities@1.2.4:
    character-entities: private
  character-reference-invalid@1.1.4:
    character-reference-invalid: private
  check-error@1.0.3:
    check-error: private
  chevrotain-allstar@0.3.1(chevrotain@11.0.3):
    chevrotain-allstar: private
  chevrotain@11.0.3:
    chevrotain: private
  chokidar@3.6.0:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  clean-stack@2.2.0:
    clean-stack: private
  cliui@6.0.0:
    cliui: private
  codepage@1.15.0:
    codepage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  commander@14.0.0:
    commander: private
  commondir@1.0.1:
    commondir: private
  compute-scroll-into-view@3.1.1:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  connect@3.7.0:
    connect: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie@1.0.2:
    cookie: private
  copy-to-clipboard@3.3.3:
    copy-to-clipboard: private
  core-js@2.6.12:
    core-js: private
  cose-base@1.0.3:
    cose-base: private
  crc-32@1.2.2:
    crc-32: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  cytoscape-cose-bilkent@4.1.0(cytoscape@3.33.0):
    cytoscape-cose-bilkent: private
  cytoscape-fcose@2.2.0(cytoscape@3.33.0):
    cytoscape-fcose: private
  cytoscape@3.33.0:
    cytoscape: private
  d3-array@2.12.1:
    d3-array: private
  d3-axis@3.0.0:
    d3-axis: private
  d3-brush@3.0.0:
    d3-brush: private
  d3-chord@3.0.1:
    d3-chord: private
  d3-color@3.1.0:
    d3-color: private
  d3-contour@4.0.2:
    d3-contour: private
  d3-delaunay@6.0.4:
    d3-delaunay: private
  d3-dispatch@3.0.1:
    d3-dispatch: private
  d3-drag@3.0.0:
    d3-drag: private
  d3-dsv@3.0.1:
    d3-dsv: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-fetch@3.0.1:
    d3-fetch: private
  d3-force@3.0.0:
    d3-force: private
  d3-format@3.1.0:
    d3-format: private
  d3-geo@3.1.1:
    d3-geo: private
  d3-hierarchy@3.1.2:
    d3-hierarchy: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-polygon@3.0.1:
    d3-polygon: private
  d3-quadtree@3.0.1:
    d3-quadtree: private
  d3-random@3.0.1:
    d3-random: private
  d3-sankey@0.12.3:
    d3-sankey: private
  d3-scale-chromatic@3.1.0:
    d3-scale-chromatic: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-selection@3.0.0:
    d3-selection: private
  d3-shape@1.3.7:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  d3-transition@3.0.1(d3-selection@3.0.0):
    d3-transition: private
  d3-zoom@3.0.0:
    d3-zoom: private
  d3@7.9.0:
    d3: private
  dagre-d3-es@7.0.11:
    dagre-d3-es: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  decamelize@1.2.0:
    decamelize: private
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: private
  deep-eql@4.1.4:
    deep-eql: private
  deep-is@0.1.4:
    deep-is: private
  default-require-extensions@3.0.1:
    default-require-extensions: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  delaunator@5.0.1:
    delaunator: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dequal@2.0.3:
    dequal: private
  detect-libc@2.0.4:
    detect-libc: private
  devlop@1.1.0:
    devlop: private
  diff@5.0.0:
    diff: private
  doctrine@2.1.0:
    doctrine: private
  dompurify@3.2.6:
    dompurify: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.193:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@1.0.2:
    encodeurl: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  es6-error@4.1.1:
    es6-error: private
  esbuild@0.25.8:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-util-is-identifier-name@3.0.0:
    estree-util-is-identifier-name: private
  esutils@2.0.3:
    esutils: private
  exsolve@1.0.7:
    exsolve: private
  extend@3.0.2:
    extend: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fault@1.0.4:
    fault: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.1.2:
    finalhandler: private
  find-cache-dir@3.3.2:
    find-cache-dir: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flat@5.0.2:
    flat: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.10:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@2.0.0:
    foreground-child: private
  form-data@4.0.4:
    form-data: private
  format@0.2.2:
    format: private
  frac@1.1.2:
    frac: private
  fromentries@1.3.2:
    fromentries: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-func-name@2.0.2:
    get-func-name: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.1.6:
    glob: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  growl@1.10.5:
    growl: private
  hachure-fill@0.5.2:
    hachure-fill: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasha@5.2.2:
    hasha: private
  hasown@2.0.2:
    hasown: private
  hast-util-parse-selector@2.2.5:
    hast-util-parse-selector: private
  hast-util-to-jsx-runtime@2.3.6:
    hast-util-to-jsx-runtime: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  hastscript@6.0.0:
    hastscript: private
  he@1.2.0:
    he: private
  highlight.js@10.7.3:
    highlight.js: private
  highlightjs-vue@1.0.0:
    highlightjs-vue: private
  html-escaper@2.0.2:
    html-escaper: private
  html-url-attributes@3.0.1:
    html-url-attributes: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ignore@7.0.5:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  inline-style-parser@0.2.4:
    inline-style-parser: private
  internal-slot@1.1.0:
    internal-slot: private
  internmap@1.0.1:
    internmap: private
  intersection-observer@0.12.2:
    intersection-observer: private
  is-alphabetical@1.0.4:
    is-alphabetical: private
  is-alphanumerical@1.0.4:
    is-alphanumerical: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-decimal@1.0.4:
    is-decimal: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@2.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@1.0.4:
    is-hexadecimal: private
  is-map@2.0.3:
    is-map: private
  is-mobile@5.0.0:
    is-mobile: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-windows@1.0.2:
    is-windows: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-hook@3.0.0:
    istanbul-lib-hook: private
  istanbul-lib-instrument@4.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-processinfo@2.0.3:
    istanbul-lib-processinfo: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jiti@2.5.1:
    jiti: private
  js-cookie@3.0.5:
    js-cookie: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json2mq@0.2.0:
    json2mq: private
  json5@2.2.3:
    json5: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  katex@0.16.22:
    katex: private
  keyv@4.5.4:
    keyv: private
  khroma@2.1.0:
    khroma: private
  kolorist@1.8.0:
    kolorist: private
  langium@3.3.1:
    langium: private
  layout-base@1.0.2:
    layout-base: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  load-tsconfig@0.2.5:
    load-tsconfig: private
  local-pkg@1.1.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.flattendeep@4.4.0:
    lodash.flattendeep: private
  lodash.merge@4.6.2:
    lodash.merge: private
  log-symbols@4.0.0:
    log-symbols: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lottie-web@5.13.0:
    lottie-web: private
  loupe@2.3.7:
    loupe: private
  lowlight@1.20.0:
    lowlight: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  make-dir@3.1.0:
    make-dir: private
  markdown-table@3.0.4:
    markdown-table: private
  marked@16.1.1:
    marked: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-mdx-expression@2.0.1:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@3.2.0:
    mdast-util-mdx-jsx: private
  mdast-util-mdxjs-esm@2.0.1:
    mdast-util-mdxjs-esm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  merge2@1.4.1:
    merge2: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  mlly@1.7.4:
    mlly: private
  mocha@8.4.0:
    mocha: private
  ms@2.1.3:
    ms: private
  nanoid@3.1.20:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-preload@0.2.1:
    node-preload: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  nyc@15.1.0:
    nyc: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.values@1.2.1:
    object.values: private
  on-finished@2.3.0:
    on-finished: private
  once@1.4.0:
    once: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@3.0.0:
    p-map: private
  p-try@2.2.0:
    p-try: private
  package-hash@4.0.0:
    package-hash: private
  package-manager-detector@1.3.0:
    package-manager-detector: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@2.0.0:
    parse-entities: private
  parseurl@1.3.3:
    parseurl: private
  path-data-parser@0.1.0:
    path-data-parser: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  pathe@2.0.3:
    pathe: private
  pathval@1.1.1:
    pathval: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pkg-types@2.2.0:
    pkg-types: private
  points-on-curve@0.2.0:
    points-on-curve: private
  points-on-path@0.2.1:
    points-on-path: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  prismjs@1.30.0:
    prismjs: private
  process-on-spawn@1.1.0:
    process-on-spawn: private
  prop-types@15.8.1:
    prop-types: private
  property-information@7.1.0:
    property-information: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  quansync@0.2.10:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  randombytes@2.1.0:
    randombytes: private
  rc-cascader@3.34.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-cascader: private
  rc-checkbox@3.5.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-checkbox: private
  rc-collapse@3.9.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-collapse: private
  rc-dialog@9.6.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-dialog: private
  rc-drawer@7.3.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-drawer: private
  rc-dropdown@4.2.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-dropdown: private
  rc-field-form@2.7.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-field-form: private
  rc-image@7.12.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-image: private
  rc-input-number@9.5.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-input-number: private
  rc-input@1.8.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-input: private
  rc-mentions@2.20.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-mentions: private
  rc-menu@9.16.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-menu: private
  rc-motion@2.9.5(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-motion: private
  rc-notification@5.6.4(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-notification: private
  rc-overflow@1.4.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-overflow: private
  rc-pagination@5.1.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-pagination: private
  rc-picker@4.11.3(dayjs@1.11.13)(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-picker: private
  rc-progress@4.0.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-progress: private
  rc-rate@2.13.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-rate: private
  rc-resize-observer@1.4.3(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-resize-observer: private
  rc-segmented@2.7.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-segmented: private
  rc-select@14.16.8(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-select: private
  rc-slider@11.1.8(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-slider: private
  rc-steps@6.0.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-steps: private
  rc-switch@4.1.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-switch: private
  rc-table@7.51.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-table: private
  rc-tabs@15.6.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-tabs: private
  rc-textarea@1.10.2(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-textarea: private
  rc-tooltip@6.4.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-tooltip: private
  rc-tree-select@5.27.0(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-tree-select: private
  rc-tree@5.13.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-tree: private
  rc-upload@4.9.2(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-upload: private
  rc-util@5.44.4(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-util: private
  rc-virtual-list@3.19.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    rc-virtual-list: private
  react-fast-compare@3.2.2:
    react-fast-compare: private
  react-is@18.3.1:
    react-is: private
  react-refresh@0.17.0:
    react-refresh: private
  react-router@7.7.1(react-dom@19.1.1(react@19.1.1))(react@19.1.1):
    react-router: private
  readdirp@3.6.0:
    readdirp: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  refractor@3.6.0:
    refractor: private
  regenerator-runtime@0.11.1:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  release-zalgo@1.0.0:
    release-zalgo: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  remark-stringify@11.0.0:
    remark-stringify: private
  require-directory@2.1.1:
    require-directory: private
  require-main-filename@2.0.0:
    require-main-filename: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve@2.0.0-next.5:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  robust-predicates@3.0.2:
    robust-predicates: private
  rollup@4.46.2:
    rollup: private
  roughjs@4.6.6:
    roughjs: private
  run-parallel@1.2.0:
    run-parallel: private
  rw@1.3.3:
    rw: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.26.0:
    scheduler: private
  screenfull@5.2.0:
    screenfull: private
  scroll-into-view-if-needed@3.1.0:
    scroll-into-view-if-needed: private
  semver@6.3.1:
    semver: private
  serialize-javascript@5.0.1:
    serialize-javascript: private
  set-blocking@2.0.0:
    set-blocking: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  spawn-wrap@2.0.0:
    spawn-wrap: private
  sprintf-js@1.0.3:
    sprintf-js: private
  ssf@0.11.2:
    ssf: private
  statuses@1.5.0:
    statuses: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  string-convert@0.2.1:
    string-convert: private
  string-width@2.1.1:
    string-width: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  style-to-js@1.1.17:
    style-to-js: private
  style-to-object@1.0.9:
    style-to-object: private
  stylis@4.3.6:
    stylis: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  synckit@0.11.11:
    synckit: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  test-exclude@6.0.0:
    test-exclude: private
  throttle-debounce@5.0.2:
    throttle-debounce: private
  tinyexec@1.0.1:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toggle-selection@1.0.6:
    toggle-selection: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  ts-api-utils@2.1.0(typescript@5.7.3):
    ts-api-utils: private
  ts-dedent@2.2.0:
    ts-dedent: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.1.0:
    type-detect: private
  type-fest@0.8.1:
    type-fest: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typedarray-to-buffer@3.1.5:
    typedarray-to-buffer: private
  ufo@1.6.1:
    ufo: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@7.8.0:
    undici-types: private
  unified@11.0.5:
    unified: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  unpipe@1.0.0:
    unpipe: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@11.1.0:
    uuid: private
  vfile-message@4.0.3:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  vscode-jsonrpc@8.2.0:
    vscode-jsonrpc: private
  vscode-languageserver-protocol@3.17.5:
    vscode-languageserver-protocol: private
  vscode-languageserver-textdocument@1.0.12:
    vscode-languageserver-textdocument: private
  vscode-languageserver-types@3.17.5:
    vscode-languageserver-types: private
  vscode-languageserver@9.0.1:
    vscode-languageserver: private
  vscode-uri@3.0.8:
    vscode-uri: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-module@2.0.1:
    which-module: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  wide-align@1.1.3:
    wide-align: private
  wmf@1.0.2:
    wmf: private
  word-wrap@1.2.5:
    word-wrap: private
  word@0.3.0:
    word: private
  workerpool@6.1.0:
    workerpool: private
  wrap-ansi@6.2.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@3.0.3:
    write-file-atomic: private
  xtend@4.0.2:
    xtend: private
  y18n@4.0.3:
    y18n: private
  yallist@5.0.0:
    yallist: private
  yargs-parser@20.2.4:
    yargs-parser: private
  yargs-unparser@2.0.0:
    yargs-unparser: private
  yargs@16.2.0:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zwitch@2.0.4:
    zwitch: private
ignoredBuilds:
  - '@tailwindcss/oxide'
  - esbuild
  - core-js
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.13.1
pendingBuilds: []
prunedAt: Thu, 31 Jul 2025 12:28:04 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-arm64@0.25.8'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.25.8'
  - '@rollup/rollup-android-arm-eabi@4.46.2'
  - '@rollup/rollup-android-arm64@4.46.2'
  - '@rollup/rollup-darwin-arm64@4.46.2'
  - '@rollup/rollup-darwin-x64@4.46.2'
  - '@rollup/rollup-freebsd-arm64@4.46.2'
  - '@rollup/rollup-freebsd-x64@4.46.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.2'
  - '@rollup/rollup-linux-arm64-gnu@4.46.2'
  - '@rollup/rollup-linux-arm64-musl@4.46.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.2'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-musl@4.46.2'
  - '@rollup/rollup-linux-s390x-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-musl@4.46.2'
  - '@rollup/rollup-win32-arm64-msvc@4.46.2'
  - '@rollup/rollup-win32-ia32-msvc@4.46.2'
  - '@tailwindcss/oxide-android-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-x64@4.1.11'
  - '@tailwindcss/oxide-freebsd-x64@4.1.11'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.11'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.11'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.11'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.11'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\JDAGENT\joyagent-jdgenie\ui\node_modules\.pnpm
virtualStoreDirMaxLength: 60
