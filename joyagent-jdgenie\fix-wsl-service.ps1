# WSL服务修复脚本
Write-Host "🔧 WSL服务修复脚本" -ForegroundColor Blue
Write-Host "=================================="

# 检查管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "❌ 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按回车键退出..."
    exit 1
}

Write-Host "✅ 管理员权限确认" -ForegroundColor Green

# 第1步：停止WSL相关服务
Write-Host ""
Write-Host "第1步：停止WSL相关服务..." -ForegroundColor Yellow
try {
    Stop-Service LxssManager -Force -ErrorAction SilentlyContinue
    Write-Host "✅ LxssManager服务已停止" -ForegroundColor Green
} catch {
    Write-Host "⚠️ LxssManager服务停止失败或未运行" -ForegroundColor Yellow
}

# 第2步：配置服务启动类型
Write-Host ""
Write-Host "第2步：配置服务启动类型..." -ForegroundColor Yellow
try {
    Set-Service LxssManager -StartupType Automatic
    Write-Host "✅ LxssManager服务设置为自动启动" -ForegroundColor Green
} catch {
    Write-Host "❌ 服务配置失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 第3步：启动WSL服务
Write-Host ""
Write-Host "第3步：启动WSL服务..." -ForegroundColor Yellow
try {
    Start-Service LxssManager
    Write-Host "✅ LxssManager服务已启动" -ForegroundColor Green
} catch {
    Write-Host "❌ 服务启动失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "尝试使用sc命令启动..." -ForegroundColor Yellow
    
    try {
        sc.exe start LxssManager
        Write-Host "✅ 使用sc命令启动成功" -ForegroundColor Green
    } catch {
        Write-Host "❌ sc命令也失败" -ForegroundColor Red
    }
}

# 第4步：检查服务状态
Write-Host ""
Write-Host "第4步：检查服务状态..." -ForegroundColor Yellow
try {
    $service = Get-Service LxssManager
    Write-Host "服务名称: $($service.Name)" -ForegroundColor Cyan
    Write-Host "服务状态: $($service.Status)" -ForegroundColor Cyan
    Write-Host "启动类型: $($service.StartType)" -ForegroundColor Cyan
    
    if ($service.Status -eq "Running") {
        Write-Host "✅ WSL服务运行正常" -ForegroundColor Green
    } else {
        Write-Host "❌ WSL服务未运行" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 无法获取服务状态" -ForegroundColor Red
}

# 第5步：测试WSL功能
Write-Host ""
Write-Host "第5步：测试WSL功能..." -ForegroundColor Yellow
try {
    $wslStatus = wsl --status 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ WSL状态检查成功" -ForegroundColor Green
        Write-Host $wslStatus
    } else {
        Write-Host "❌ WSL状态检查失败" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ WSL命令执行失败" -ForegroundColor Red
}

# 第6步：设置WSL默认版本
Write-Host ""
Write-Host "第6步：设置WSL默认版本..." -ForegroundColor Yellow
try {
    wsl --set-default-version 2 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ WSL默认版本设置为2" -ForegroundColor Green
    } else {
        Write-Host "⚠️ WSL版本设置可能失败" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ WSL版本设置失败" -ForegroundColor Red
}

Write-Host ""
Write-Host "=================================="
Write-Host "🎉 WSL服务修复完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 下一步操作：" -ForegroundColor Blue
Write-Host "1. 安装Ubuntu: wsl --install -d Ubuntu --no-launch" -ForegroundColor Cyan
Write-Host "2. 重启Docker Desktop" -ForegroundColor Cyan
Write-Host "3. 运行: docker-start.sh" -ForegroundColor Cyan
Write-Host "=================================="

Read-Host "按回车键继续..."
