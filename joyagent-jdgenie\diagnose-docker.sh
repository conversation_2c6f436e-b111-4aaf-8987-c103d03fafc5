#!/bin/bash

echo "🔍 Docker诊断脚本"
echo "=================================="

echo "1. 检查Docker版本..."
docker --version 2>/dev/null || echo "❌ Docker命令不可用"

echo ""
echo "2. 检查Docker Compose版本..."
docker-compose --version 2>/dev/null || echo "❌ Docker Compose命令不可用"

echo ""
echo "3. 测试简单Docker命令..."
timeout 10 docker ps 2>/dev/null && echo "✅ docker ps 成功" || echo "❌ docker ps 失败或超时"

echo ""
echo "4. 测试Docker info..."
timeout 10 docker info >/dev/null 2>&1 && echo "✅ docker info 成功" || echo "❌ docker info 失败或超时"

echo ""
echo "5. 检查Docker进程..."
tasklist | findstr -i docker 2>/dev/null || echo "❌ 未找到Docker进程"

echo ""
echo "6. 测试网络连接..."
ping -n 1 8.8.8.8 >nul 2>&1 && echo "✅ 网络连接正常" || echo "❌ 网络连接异常"

echo ""
echo "7. 检查端口占用..."
netstat -an | findstr ":2375\|:2376" 2>/dev/null || echo "Docker端口未监听"

echo ""
echo "=================================="
echo "诊断完成"
