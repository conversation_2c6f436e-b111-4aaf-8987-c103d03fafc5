#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 JoyAgent-JDGenie 生产环境部署脚本${NC}"
echo "=================================="

# 配置变量
DOMAIN="your-domain.com"
EMAIL="<EMAIL>"
DATA_DIR="/opt/genie"
BACKUP_DIR="/opt/genie/backups"

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        echo -e "${RED}❌ 此脚本需要root权限运行${NC}"
        echo "请使用: sudo $0"
        exit 1
    fi
    echo -e "${GREEN}✅ 权限检查通过${NC}"
}

# 系统环境检查
check_system() {
    echo -e "${BLUE}🔍 系统环境检查${NC}"
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        echo -e "${RED}❌ 无法识别操作系统${NC}"
        exit 1
    fi
    
    source /etc/os-release
    echo -e "${GREEN}✅ 操作系统: $PRETTY_NAME${NC}"
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $MEMORY_GB -lt 4 ]]; then
        echo -e "${YELLOW}⚠️ 内存不足4GB，建议升级${NC}"
    else
        echo -e "${GREEN}✅ 内存: ${MEMORY_GB}GB${NC}"
    fi
    
    # 检查磁盘空间
    DISK_AVAILABLE=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ $DISK_AVAILABLE -lt 20 ]]; then
        echo -e "${RED}❌ 磁盘空间不足20GB${NC}"
        exit 1
    else
        echo -e "${GREEN}✅ 磁盘空间: ${DISK_AVAILABLE}GB可用${NC}"
    fi
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}📦 安装系统依赖${NC}"
    
    # 更新包管理器
    apt-get update -y
    
    # 安装基础工具
    apt-get install -y \
        curl \
        wget \
        git \
        unzip \
        htop \
        nginx \
        certbot \
        python3-certbot-nginx \
        fail2ban \
        ufw
    
    # 安装Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${BLUE}安装Docker...${NC}"
        curl -fsSL https://get.docker.com -o get-docker.sh
        sh get-docker.sh
        systemctl enable docker
        systemctl start docker
        rm get-docker.sh
    fi
    
    # 安装Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${BLUE}安装Docker Compose...${NC}"
        curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
    fi
    
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 配置防火墙
configure_firewall() {
    echo -e "${BLUE}🔥 配置防火墙${NC}"
    
    # 重置UFW
    ufw --force reset
    
    # 默认策略
    ufw default deny incoming
    ufw default allow outgoing
    
    # 允许SSH
    ufw allow ssh
    
    # 允许HTTP/HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # 启用防火墙
    ufw --force enable
    
    echo -e "${GREEN}✅ 防火墙配置完成${NC}"
}

# 创建目录结构
create_directories() {
    echo -e "${BLUE}📁 创建目录结构${NC}"
    
    mkdir -p $DATA_DIR/{data,logs,config,ssl,backups}
    mkdir -p /etc/nginx/sites-available
    mkdir -p /etc/nginx/sites-enabled
    
    # 设置权限
    chown -R 1000:1000 $DATA_DIR/data
    chown -R 1000:1000 $DATA_DIR/logs
    chmod -R 755 $DATA_DIR
    
    echo -e "${GREEN}✅ 目录结构创建完成${NC}"
}

# 配置SSL证书
configure_ssl() {
    echo -e "${BLUE}🔒 配置SSL证书${NC}"
    
    # 停止nginx以释放80端口
    systemctl stop nginx
    
    # 获取Let's Encrypt证书
    certbot certonly --standalone \
        --non-interactive \
        --agree-tos \
        --email $EMAIL \
        -d $DOMAIN \
        -d www.$DOMAIN
    
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}✅ SSL证书获取成功${NC}"
        
        # 复制证书到项目目录
        cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem $DATA_DIR/ssl/cert.pem
        cp /etc/letsencrypt/live/$DOMAIN/privkey.pem $DATA_DIR/ssl/key.pem
        
        # 设置证书自动续期
        echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
    else
        echo -e "${YELLOW}⚠️ SSL证书获取失败，将使用自签名证书${NC}"
        
        # 生成自签名证书
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout $DATA_DIR/ssl/key.pem \
            -out $DATA_DIR/ssl/cert.pem \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=$DOMAIN"
    fi
}

# 部署应用
deploy_application() {
    echo -e "${BLUE}🚀 部署应用${NC}"
    
    # 进入项目目录
    cd /opt/genie
    
    # 克隆或更新代码
    if [[ -d "joyagent-jdgenie" ]]; then
        cd joyagent-jdgenie
        git pull
    else
        git clone https://github.com/jd-opensource/joyagent-jdgenie.git
        cd joyagent-jdgenie
    fi
    
    # 更新配置文件中的域名
    sed -i "s/your-domain.com/$DOMAIN/g" nginx/nginx.conf
    sed -i "s/your-domain.com/$DOMAIN/g" docker-compose.prod.yml
    
    # 构建并启动服务
    docker-compose -f docker-compose.prod.yml down
    docker-compose -f docker-compose.prod.yml up -d --build
    
    echo -e "${GREEN}✅ 应用部署完成${NC}"
}

# 配置监控
configure_monitoring() {
    echo -e "${BLUE}📊 配置监控${NC}"
    
    # 创建监控脚本
    cat > /opt/genie/monitor.sh << 'EOF'
#!/bin/bash
LOG_FILE="/opt/genie/logs/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查服务状态
if ! curl -s http://localhost:3000 > /dev/null; then
    echo "[$DATE] ERROR: Frontend service is down" >> $LOG_FILE
    docker-compose -f /opt/genie/joyagent-jdgenie/docker-compose.prod.yml restart genie-app
fi

if ! curl -s http://localhost:8080/web/health > /dev/null; then
    echo "[$DATE] ERROR: Backend service is down" >> $LOG_FILE
    docker-compose -f /opt/genie/joyagent-jdgenie/docker-compose.prod.yml restart genie-app
fi

# 检查磁盘使用率
DISK_USAGE=$(df / | awk 'NR==2{print $5}' | sed 's/%//')
if [[ $DISK_USAGE -gt 80 ]]; then
    echo "[$DATE] WARNING: Disk usage is ${DISK_USAGE}%" >> $LOG_FILE
fi

# 检查内存使用率
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [[ $MEMORY_USAGE -gt 80 ]]; then
    echo "[$DATE] WARNING: Memory usage is ${MEMORY_USAGE}%" >> $LOG_FILE
fi
EOF

    chmod +x /opt/genie/monitor.sh
    
    # 添加到crontab
    echo "*/5 * * * * /opt/genie/monitor.sh" | crontab -
    
    echo -e "${GREEN}✅ 监控配置完成${NC}"
}

# 配置备份
configure_backup() {
    echo -e "${BLUE}💾 配置备份${NC}"
    
    # 创建备份脚本
    cat > /opt/genie/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/genie/backups"
DATE=$(date '+%Y%m%d_%H%M%S')
BACKUP_FILE="genie_backup_$DATE.tar.gz"

# 创建备份
cd /opt/genie
tar -czf $BACKUP_DIR/$BACKUP_FILE \
    --exclude='joyagent-jdgenie/.git' \
    --exclude='logs/*.log' \
    joyagent-jdgenie/ data/ config/

# 保留最近7天的备份
find $BACKUP_DIR -name "genie_backup_*.tar.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_FILE"
EOF

    chmod +x /opt/genie/backup.sh
    
    # 添加到crontab（每天凌晨2点备份）
    echo "0 2 * * * /opt/genie/backup.sh" | crontab -
    
    echo -e "${GREEN}✅ 备份配置完成${NC}"
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "=================================="
    echo -e "${GREEN}🎉 生产环境部署完成！${NC}"
    echo "=================================="
    echo -e "${BLUE}访问信息：${NC}"
    echo -e " 🌐 主站: https://$DOMAIN"
    echo -e " 🔧 API: https://$DOMAIN/api/"
    echo -e " 📊 监控: https://$DOMAIN:8090/nginx_status"
    echo ""
    echo -e "${BLUE}管理命令：${NC}"
    echo -e " 查看状态: docker-compose -f /opt/genie/joyagent-jdgenie/docker-compose.prod.yml ps"
    echo -e " 查看日志: docker-compose -f /opt/genie/joyagent-jdgenie/docker-compose.prod.yml logs -f"
    echo -e " 重启服务: docker-compose -f /opt/genie/joyagent-jdgenie/docker-compose.prod.yml restart"
    echo -e " 手动备份: /opt/genie/backup.sh"
    echo ""
    echo -e "${BLUE}重要文件位置：${NC}"
    echo -e " 数据目录: /opt/genie/data"
    echo -e " 日志目录: /opt/genie/logs"
    echo -e " 备份目录: /opt/genie/backups"
    echo -e " SSL证书: /opt/genie/ssl"
    echo "=================================="
}

# 主函数
main() {
    echo -e "${BLUE}开始生产环境部署...${NC}"
    
    # 检查参数
    if [[ $# -eq 2 ]]; then
        DOMAIN=$1
        EMAIL=$2
    else
        echo -e "${YELLOW}使用方法: $0 <domain> <email>${NC}"
        echo -e "${YELLOW}示例: $0 genie.example.com <EMAIL>${NC}"
        echo -e "${YELLOW}使用默认配置继续部署...${NC}"
    fi
    
    check_root
    check_system
    install_dependencies
    configure_firewall
    create_directories
    configure_ssl
    deploy_application
    configure_monitoring
    configure_backup
    show_deployment_info
    
    echo -e "${GREEN}🎉 部署完成！请访问 https://$DOMAIN 开始使用${NC}"
}

# 运行主函数
main "$@"
