# Windows功能检查脚本
Write-Host "🔍 检查Windows功能和Docker前置条件" -ForegroundColor Blue
Write-Host "=================================="

# 检查Windows版本
$osInfo = Get-WmiObject -Class Win32_OperatingSystem
Write-Host "操作系统: $($osInfo.Caption)" -ForegroundColor Green
Write-Host "版本: $($osInfo.Version)" -ForegroundColor Green
Write-Host "架构: $($osInfo.OSArchitecture)" -ForegroundColor Green

Write-Host ""

# 检查Hyper-V
Write-Host "检查Hyper-V状态..." -ForegroundColor Yellow
$hyperv = Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All
if ($hyperv.State -eq "Enabled") {
    Write-Host "✅ Hyper-V 已启用" -ForegroundColor Green
} else {
    Write-Host "❌ Hyper-V 未启用" -ForegroundColor Red
}

# 检查WSL
Write-Host "检查WSL状态..." -ForegroundColor Yellow
try {
    $wsl = wsl --status 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ WSL 已安装" -ForegroundColor Green
        wsl --status
    } else {
        Write-Host "❌ WSL 未安装" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ WSL 未安装" -ForegroundColor Red
}

# 检查虚拟化支持
Write-Host ""
Write-Host "检查虚拟化支持..." -ForegroundColor Yellow
$virtualization = Get-WmiObject -Class Win32_Processor | Select-Object -ExpandProperty VirtualizationFirmwareEnabled
if ($virtualization) {
    Write-Host "✅ 硬件虚拟化已启用" -ForegroundColor Green
} else {
    Write-Host "❌ 硬件虚拟化未启用" -ForegroundColor Red
}

# 检查Docker Desktop
Write-Host ""
Write-Host "检查Docker Desktop..." -ForegroundColor Yellow
$dockerPath = "C:\Program Files\Docker\Docker\Docker Desktop.exe"
if (Test-Path $dockerPath) {
    Write-Host "✅ Docker Desktop 已安装" -ForegroundColor Green
} else {
    Write-Host "❌ Docker Desktop 未找到" -ForegroundColor Red
}

Write-Host ""
Write-Host "=================================="
Write-Host "检查完成！" -ForegroundColor Blue
