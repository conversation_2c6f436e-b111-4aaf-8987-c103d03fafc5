# 中国大陆优化启动脚本
Write-Host "🇨🇳 JoyAgent-JDGenie 中国大陆优化启动脚本" -ForegroundColor Blue
Write-Host "=================================="

# 第1步：配置所有镜像源
Write-Host "第1步：配置镜像源..." -ForegroundColor Yellow

# 配置Docker镜像源
$dockerConfigDir = "$env:USERPROFILE\.docker"
if (-not (Test-Path $dockerConfigDir)) {
    New-Item -ItemType Directory -Path $dockerConfigDir -Force
}

$daemonConfig = @{
    "registry-mirrors" = @(
        "https://docker.m.daocloud.io",
        "https://dockerproxy.com",
        "https://mirror.baidubce.com",
        "https://reg-mirror.qiniu.com",
        "https://registry.docker-cn.com"
    )
    "insecure-registries" = @()
    "debug" = $false
    "experimental" = $false
    "builder" = @{
        "gc" = @{
            "defaultKeepStorage" = "20GB"
            "enabled" = $true
        }
    }
    "max-concurrent-downloads" = 3
    "max-concurrent-uploads" = 5
}

$daemonConfigPath = "$dockerConfigDir\daemon.json"
$daemonConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $daemonConfigPath -Encoding UTF8
Write-Host "✅ Docker镜像源配置完成" -ForegroundColor Green

# 第2步：重启Docker Desktop
Write-Host ""
Write-Host "第2步：重启Docker Desktop..." -ForegroundColor Yellow

try {
    Get-Process "Docker Desktop" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 5
    Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    Write-Host "✅ Docker Desktop已重启" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Desktop重启失败" -ForegroundColor Red
}

# 第3步：等待Docker启动
Write-Host ""
Write-Host "第3步：等待Docker启动..." -ForegroundColor Yellow

$maxAttempts = 30
$attempt = 0
$dockerReady = $false

while ($attempt -lt $maxAttempts -and -not $dockerReady) {
    $attempt++
    Write-Host "检查Docker状态... ($attempt/$maxAttempts)" -NoNewline
    
    try {
        docker info *>$null 2>&1
        if ($LASTEXITCODE -eq 0) {
            $dockerReady = $true
            Write-Host " ✅" -ForegroundColor Green
            break
        }
    } catch {}
    
    Write-Host " ⏳" -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

if (-not $dockerReady) {
    Write-Host "❌ Docker启动超时" -ForegroundColor Red
    exit 1
}

# 第4步：预拉取基础镜像（使用国内镜像源）
Write-Host ""
Write-Host "第4步：预拉取基础镜像..." -ForegroundColor Yellow

$baseImages = @(
    "hello-world",
    "alpine:latest",
    "node:18-alpine",
    "openjdk:17-jre-slim",
    "python:3.11-slim"
)

foreach ($image in $baseImages) {
    Write-Host "拉取镜像: $image" -ForegroundColor Cyan
    try {
        docker pull $image *>$null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $image 拉取成功" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $image 拉取失败，但不影响继续" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ $image 拉取异常" -ForegroundColor Yellow
    }
}

# 第5步：修改docker-compose.yml使用国内镜像
Write-Host ""
Write-Host "第5步：优化docker-compose配置..." -ForegroundColor Yellow

if (Test-Path "docker-compose.yml") {
    # 备份原始文件
    Copy-Item "docker-compose.yml" "docker-compose.yml.backup"
    
    # 读取并修改配置
    $composeContent = Get-Content "docker-compose.yml" -Raw
    
    # 替换基础镜像为国内镜像
    $composeContent = $composeContent -replace "docker\.m\.daocloud\.io/library/node:20-alpine", "docker.m.daocloud.io/library/node:18-alpine"
    $composeContent = $composeContent -replace "docker\.m\.daocloud\.io/library/maven:3\.8-openjdk-17", "docker.m.daocloud.io/library/maven:3.8-openjdk-17"
    $composeContent = $composeContent -replace "docker\.m\.daocloud\.io/library/python:3\.11-slim", "docker.m.daocloud.io/library/python:3.11-slim"
    
    $composeContent | Out-File "docker-compose.yml" -Encoding UTF8
    Write-Host "✅ docker-compose配置已优化" -ForegroundColor Green
}

# 第6步：启动JoyAgent-JDGenie
Write-Host ""
Write-Host "第6步：启动JoyAgent-JDGenie..." -ForegroundColor Yellow

try {
    if (Test-Path "docker-start.sh") {
        Write-Host "使用docker-start.sh启动..."
        bash docker-start.sh
    } else {
        Write-Host "使用docker-compose启动..."
        docker-compose up -d --build
    }
    
    Write-Host "✅ JoyAgent-JDGenie启动命令已执行" -ForegroundColor Green
} catch {
    Write-Host "❌ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 第7步：等待服务启动
Write-Host ""
Write-Host "第7步：等待服务启动..." -ForegroundColor Yellow

$services = @(
    @{Name="前端服务"; Port=3000; Url="http://localhost:3000"},
    @{Name="后端服务"; Port=8080; Url="http://localhost:8080/web/health"},
    @{Name="工具服务"; Port=1601; Url="http://localhost:1601"},
    @{Name="MCP客户端"; Port=8188; Url="http://localhost:8188"}
)

$maxWait = 300  # 5分钟
$startTime = Get-Date

while ((Get-Date) -lt $startTime.AddSeconds($maxWait)) {
    $readyServices = 0
    
    foreach ($service in $services) {
        try {
            $response = Invoke-WebRequest -Uri $service.Url -TimeoutSec 2 -UseBasicParsing -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                $readyServices++
            }
        } catch {}
    }
    
    $elapsed = [math]::Round(((Get-Date) - $startTime).TotalSeconds)
    Write-Host "服务启动进度: $readyServices/$($services.Count) ($elapsed 秒)" -ForegroundColor Cyan
    
    if ($readyServices -eq $services.Count) {
        Write-Host "✅ 所有服务已启动！" -ForegroundColor Green
        break
    }
    
    Start-Sleep -Seconds 10
}

# 第8步：显示最终状态
Write-Host ""
Write-Host "=================================="
Write-Host "🎉 JoyAgent-JDGenie 启动完成！" -ForegroundColor Green
Write-Host "=================================="

Write-Host "📊 服务状态检查：" -ForegroundColor Blue
foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri $service.Url -TimeoutSec 5 -UseBasicParsing -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($service.Name): 运行正常" -ForegroundColor Green
        } else {
            Write-Host "❌ $($service.Name): 状态异常" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $($service.Name): 无法访问" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🌐 访问地址：" -ForegroundColor Blue
Write-Host "• 主界面: http://localhost:3000" -ForegroundColor Cyan
Write-Host "• 后端API: http://localhost:8080" -ForegroundColor Cyan
Write-Host "• 工具服务: http://localhost:1601" -ForegroundColor Cyan
Write-Host "• MCP客户端: http://localhost:8188" -ForegroundColor Cyan

Write-Host ""
Write-Host "🔧 已配置的LLM服务：" -ForegroundColor Blue
Write-Host "• DeepSeek: ***********************************" -ForegroundColor Cyan
Write-Host "• 火山方舟: bcdd2ded-7352-4668-9fef-ebe31b56177a" -ForegroundColor Cyan
Write-Host "• Serper搜索: 04d74f4080b9379d5875e04bccc3000aa8e7840f" -ForegroundColor Cyan

Write-Host ""
Write-Host "💡 使用提示：" -ForegroundColor Blue
Write-Host "• 首次启动可能需要较长时间下载镜像" -ForegroundColor Cyan
Write-Host "• 已配置国内镜像源，下载速度更快" -ForegroundColor Cyan
Write-Host "• 如有问题，请查看: docker-compose logs -f" -ForegroundColor Cyan

Write-Host "=================================="

# 询问是否打开浏览器
$choice = Read-Host "是否打开浏览器访问主界面？(Y/N)"
if ($choice -eq "Y" -or $choice -eq "y") {
    Start-Process "http://localhost:3000"
}

Read-Host "按回车键退出..."
