# 快速启动脚本 (中国大陆优化)
Write-Host "🚀 JoyAgent-JDGenie 快速启动 (中国大陆优化)" -ForegroundColor Blue
Write-Host "=================================="

# 配置Docker镜像源
Write-Host "配置Docker镜像源..." -ForegroundColor Yellow
$dockerConfigDir = "$env:USERPROFILE\.docker"
if (-not (Test-Path $dockerConfigDir)) {
    New-Item -ItemType Directory -Path $dockerConfigDir -Force
}

$daemonJson = @"
{
  "registry-mirrors": [
    "https://docker.m.daocloud.io",
    "https://dockerproxy.com",
    "https://mirror.baidubce.com",
    "https://reg-mirror.qiniu.com"
  ],
  "builder": {
    "gc": {
      "defaultKeepStorage": "20GB",
      "enabled": true
    }
  }
}
"@

$daemonJson | Out-File "$dockerConfigDir\daemon.json" -Encoding UTF8
Write-Host "✅ Docker镜像源配置完成" -ForegroundColor Green

# 重启Docker Desktop
Write-Host "重启Docker Desktop..." -ForegroundColor Yellow
try {
    Stop-Process -Name "Docker Desktop" -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 5
    Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    Write-Host "✅ Docker Desktop已重启" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Docker Desktop重启可能失败" -ForegroundColor Yellow
}

# 等待Docker启动
Write-Host "等待Docker启动..." -ForegroundColor Yellow
$attempts = 0
do {
    $attempts++
    Write-Host "检查Docker状态... ($attempts/30)" -NoNewline
    Start-Sleep -Seconds 5
    
    try {
        docker info *>$null 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host " ✅" -ForegroundColor Green
            break
        }
    } catch {}
    
    Write-Host " ⏳" -ForegroundColor Yellow
} while ($attempts -lt 30)

if ($attempts -ge 30) {
    Write-Host "❌ Docker启动超时，请手动检查Docker Desktop" -ForegroundColor Red
    Read-Host "按回车键退出..."
    exit 1
}

# 启动JoyAgent-JDGenie
Write-Host "启动JoyAgent-JDGenie..." -ForegroundColor Yellow
try {
    docker-compose up -d --build
    Write-Host "✅ 启动命令已执行" -ForegroundColor Green
} catch {
    Write-Host "❌ 启动失败" -ForegroundColor Red
    Read-Host "按回车键退出..."
    exit 1
}

# 等待服务启动
Write-Host "等待服务启动..." -ForegroundColor Yellow
$serviceChecks = 0
do {
    $serviceChecks++
    $readyCount = 0
    
    # 检查各个服务
    try { if ((Invoke-WebRequest "http://localhost:3000" -TimeoutSec 2 -UseBasicParsing -ErrorAction SilentlyContinue).StatusCode -eq 200) { $readyCount++ } } catch {}
    try { if ((Invoke-WebRequest "http://localhost:8080" -TimeoutSec 2 -UseBasicParsing -ErrorAction SilentlyContinue).StatusCode -eq 200) { $readyCount++ } } catch {}
    try { if ((Invoke-WebRequest "http://localhost:1601" -TimeoutSec 2 -UseBasicParsing -ErrorAction SilentlyContinue).StatusCode -eq 200) { $readyCount++ } } catch {}
    try { if ((Invoke-WebRequest "http://localhost:8188" -TimeoutSec 2 -UseBasicParsing -ErrorAction SilentlyContinue).StatusCode -eq 200) { $readyCount++ } } catch {}
    
    Write-Host "服务启动进度: $readyCount/4 ($serviceChecks/30)" -ForegroundColor Cyan
    
    if ($readyCount -eq 4) {
        Write-Host "✅ 所有服务已启动！" -ForegroundColor Green
        break
    }
    
    Start-Sleep -Seconds 10
} while ($serviceChecks -lt 30)

# 显示结果
Write-Host ""
Write-Host "=================================="
Write-Host "🎉 JoyAgent-JDGenie 启动完成！" -ForegroundColor Green
Write-Host "=================================="
Write-Host "🌐 访问地址: http://localhost:3000" -ForegroundColor Cyan
Write-Host "🔧 后端API: http://localhost:8080" -ForegroundColor Cyan
Write-Host "🛠️ 工具服务: http://localhost:1601" -ForegroundColor Cyan
Write-Host "🔌 MCP客户端: http://localhost:8188" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 已配置DeepSeek、火山方舟和Serper搜索" -ForegroundColor Cyan
Write-Host "=================================="

$openBrowser = Read-Host "是否打开浏览器？(Y/N)"
if ($openBrowser -eq "Y" -or $openBrowser -eq "y") {
    Start-Process "http://localhost:3000"
}

Read-Host "按回车键退出..."
