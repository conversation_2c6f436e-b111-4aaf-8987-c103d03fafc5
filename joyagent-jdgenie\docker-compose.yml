version: '3.8'

services:
  genie-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: joyagent-jdgenie
    ports:
      - "3000:3000"    # 前端服务
      - "8080:8080"    # 后端服务
      - "1601:1601"    # 工具服务
      - "8188:8188"    # MCP客户端
    environment:
      # DeepSeek 配置
      - OPENAI_API_KEY=***********************************
      - OPENAI_BASE_URL=https://api.deepseek.com/v1
      - DEFAULT_MODEL=deepseek/deepseek-chat
      
      # 火山方舟配置 (备用)
      - DOUBAO_API_KEY=bcdd2ded-7352-4668-9fef-ebe31b56177a
      - DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
      
      # 搜索配置
      - SERPER_SEARCH_API_KEY=04d74f4080b9379d5875e04bccc3000aa8e7840f
      - SERPER_SEARCH_URL=https://google.serper.dev/search
      - USE_SEARCH_ENGINE=serp
      
      # 其他配置
      - SENSITIVE_WORD_REPLACE=true
      - FILE_SAVE_PATH=file_db_dir
      - SQLITE_DB_PATH=autobots.db
      - FILE_SERVER_URL=http://127.0.0.1:1601/v1/file_tool
    volumes:
      - ./data:/app/data  # 持久化数据
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000", "&&", "curl", "-f", "http://localhost:8080/web/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  default:
    name: genie-network
