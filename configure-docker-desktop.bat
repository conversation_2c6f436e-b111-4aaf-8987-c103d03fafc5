@echo off
echo 🐳 Docker Desktop 配置脚本
echo ==================================

echo 1. 停止Docker Desktop...
taskkill /F /IM "Docker Desktop.exe" 2>nul
timeout /t 5 /nobreak >nul

echo 2. 检查Docker Desktop安装...
if exist "C:\Program Files\Docker\Docker\Docker Desktop.exe" (
    echo ✅ Docker Desktop 已安装
) else (
    echo ❌ Docker Desktop 未安装
    echo 请从 https://www.docker.com/products/docker-desktop/ 下载安装
    pause
    exit /b 1
)

echo.
echo 3. 配置Docker Desktop设置...
set DOCKER_CONFIG_DIR=%APPDATA%\Docker

if not exist "%DOCKER_CONFIG_DIR%" mkdir "%DOCKER_CONFIG_DIR%"

echo 创建Docker配置文件...
(
echo {
echo   "builder": {
echo     "gc": {
echo       "defaultKeepStorage": "20GB",
echo       "enabled": true
echo     }
echo   },
echo   "experimental": false,
echo   "useWSL2": true,
echo   "wslEngineEnabled": true
echo }
) > "%DOCKER_CONFIG_DIR%\settings.json"

echo.
echo 4. 启动Docker Desktop...
start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"

echo.
echo 5. 等待Docker Desktop启动...
echo 请等待Docker Desktop完全启动（系统托盘图标变绿）

echo.
echo ==================================
echo ✅ Docker Desktop配置完成！
echo.
echo 💡 下一步操作：
echo 1. 等待Docker Desktop完全启动
echo 2. 运行: docker info 验证
echo 3. 然后运行: docker-start.sh
echo ==================================
pause
