#Requires -Version 5.1
<#
.SYNOPSIS
    JoyAgent-JDGenie 专业启动脚本 (中国大陆优化版)

.DESCRIPTION
    此脚本用于在中国大陆环境下优化启动JoyAgent-JDGenie多智能体系统。
    包含Docker镜像源配置、服务启动、状态检查等完整功能。

.PARAMETER SkipMirrorConfig
    跳过Docker镜像源配置

.PARAMETER Timeout
    服务启动超时时间（秒），默认300秒

.EXAMPLE
    .\professional-start.ps1
    使用默认配置启动

.EXAMPLE
    .\professional-start.ps1 -SkipMirrorConfig -Timeout 600
    跳过镜像源配置，设置超时时间为10分钟

.NOTES
    Author: JoyAgent Team
    Version: 1.0
    Created: 2025-08-01
#>

[CmdletBinding()]
param(
    [switch]$SkipMirrorConfig,
    [int]$Timeout = 300
)

# 设置错误处理
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# 全局变量
$Script:LogFile = "startup-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"
$Script:Services = @(
    @{ Name = "前端服务"; Url = "http://localhost:3000"; Port = 3000 },
    @{ Name = "后端服务"; Url = "http://localhost:8080"; Port = 8080 },
    @{ Name = "工具服务"; Url = "http://localhost:1601"; Port = 1601 },
    @{ Name = "MCP客户端"; Url = "http://localhost:8188"; Port = 8188 }
)

#region 辅助函数

function Write-Log {
    param(
        [string]$Message,
        [ValidateSet("Info", "Warning", "Error", "Success")]
        [string]$Level = "Info"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # 输出到控制台
    switch ($Level) {
        "Info"    { Write-Host $Message -ForegroundColor Cyan }
        "Warning" { Write-Host $Message -ForegroundColor Yellow }
        "Error"   { Write-Host $Message -ForegroundColor Red }
        "Success" { Write-Host $Message -ForegroundColor Green }
    }
    
    # 输出到日志文件
    Add-Content -Path $Script:LogFile -Value $logMessage -Encoding UTF8
}

function Test-DockerRunning {
    try {
        $null = docker info 2>$null
        return $LASTEXITCODE -eq 0
    }
    catch {
        return $false
    }
}

function Test-ServiceAvailable {
    param([string]$Url, [int]$TimeoutSeconds = 5)
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSeconds -UseBasicParsing -ErrorAction SilentlyContinue
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

function Wait-ForDocker {
    param([int]$MaxAttempts = 60)
    
    Write-Log "等待Docker Engine启动..." "Info"
    
    for ($i = 1; $i -le $MaxAttempts; $i++) {
        Write-Progress -Activity "等待Docker启动" -Status "尝试 $i/$MaxAttempts" -PercentComplete (($i / $MaxAttempts) * 100)
        
        if (Test-DockerRunning) {
            Write-Progress -Activity "等待Docker启动" -Completed
            Write-Log "Docker Engine已就绪" "Success"
            return $true
        }
        
        Start-Sleep -Seconds 5
    }
    
    Write-Progress -Activity "等待Docker启动" -Completed
    Write-Log "Docker启动超时" "Error"
    return $false
}

function Wait-ForServices {
    param([int]$TimeoutSeconds = $Timeout)
    
    Write-Log "等待服务启动..." "Info"
    $startTime = Get-Date
    $endTime = $startTime.AddSeconds($TimeoutSeconds)
    
    while ((Get-Date) -lt $endTime) {
        $readyCount = 0
        
        foreach ($service in $Script:Services) {
            if (Test-ServiceAvailable -Url $service.Url) {
                $readyCount++
            }
        }
        
        $elapsed = [math]::Round(((Get-Date) - $startTime).TotalSeconds)
        $progress = [math]::Min(100, ($readyCount / $Script:Services.Count) * 100)
        
        Write-Progress -Activity "等待服务启动" -Status "已启动 $readyCount/$($Script:Services.Count) 个服务 (${elapsed}s)" -PercentComplete $progress
        
        if ($readyCount -eq $Script:Services.Count) {
            Write-Progress -Activity "等待服务启动" -Completed
            Write-Log "所有服务已启动" "Success"
            return $true
        }
        
        Start-Sleep -Seconds 10
    }
    
    Write-Progress -Activity "等待服务启动" -Completed
    Write-Log "服务启动超时" "Warning"
    return $false
}

#endregion

#region 主要功能函数

function Initialize-DockerMirrors {
    if ($SkipMirrorConfig) {
        Write-Log "跳过Docker镜像源配置" "Info"
        return
    }
    
    Write-Log "配置Docker镜像源..." "Info"
    
    try {
        $dockerConfigDir = Join-Path $env:USERPROFILE ".docker"
        
        if (-not (Test-Path $dockerConfigDir)) {
            New-Item -ItemType Directory -Path $dockerConfigDir -Force | Out-Null
        }
        
        $daemonConfig = @{
            "registry-mirrors" = @(
                "https://docker.m.daocloud.io",
                "https://dockerproxy.com",
                "https://mirror.baidubce.com",
                "https://reg-mirror.qiniu.com"
            )
            "builder" = @{
                "gc" = @{
                    "defaultKeepStorage" = "20GB"
                    "enabled" = $true
                }
            }
            "max-concurrent-downloads" = 3
            "max-concurrent-uploads" = 5
        }
        
        $configPath = Join-Path $dockerConfigDir "daemon.json"
        $daemonConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $configPath -Encoding UTF8
        
        Write-Log "Docker镜像源配置完成: $configPath" "Success"
    }
    catch {
        Write-Log "Docker镜像源配置失败: $($_.Exception.Message)" "Error"
        throw
    }
}

function Restart-DockerDesktop {
    Write-Log "重启Docker Desktop..." "Info"
    
    try {
        # 停止Docker Desktop
        Get-Process -Name "Docker Desktop" -ErrorAction SilentlyContinue | Stop-Process -Force
        Start-Sleep -Seconds 5
        
        # 启动Docker Desktop
        $dockerPath = "C:\Program Files\Docker\Docker\Docker Desktop.exe"
        if (-not (Test-Path $dockerPath)) {
            throw "Docker Desktop未找到: $dockerPath"
        }
        
        Start-Process -FilePath $dockerPath
        Write-Log "Docker Desktop已启动" "Success"
    }
    catch {
        Write-Log "Docker Desktop重启失败: $($_.Exception.Message)" "Error"
        throw
    }
}

function Test-DockerMirrors {
    Write-Log "测试Docker镜像源..." "Info"
    
    try {
        $testImage = "hello-world"
        docker pull $testImage 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Docker镜像源工作正常" "Success"
            return $true
        }
        else {
            Write-Log "Docker镜像拉取失败，但不影响继续" "Warning"
            return $false
        }
    }
    catch {
        Write-Log "Docker镜像源测试异常: $($_.Exception.Message)" "Warning"
        return $false
    }
}

function Start-JoyAgentServices {
    Write-Log "启动JoyAgent-JDGenie服务..." "Info"
    
    try {
        if (-not (Test-Path "docker-compose.yml")) {
            throw "docker-compose.yml文件未找到"
        }
        
        # 启动服务
        docker-compose up -d --build
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "JoyAgent-JDGenie服务启动命令执行成功" "Success"
            return $true
        }
        else {
            throw "docker-compose命令执行失败，退出代码: $LASTEXITCODE"
        }
    }
    catch {
        Write-Log "服务启动失败: $($_.Exception.Message)" "Error"
        throw
    }
}

function Show-ServiceStatus {
    Write-Log "检查服务状态..." "Info"
    
    Write-Host "`n=================================="
    Write-Host "📊 服务状态检查" -ForegroundColor Blue
    Write-Host "=================================="
    
    foreach ($service in $Script:Services) {
        $isAvailable = Test-ServiceAvailable -Url $service.Url
        $status = if ($isAvailable) { "✅ 运行正常" } else { "❌ 无法访问" }
        $color = if ($isAvailable) { "Green" } else { "Red" }
        
        Write-Host "$($service.Name): $status" -ForegroundColor $color
    }
}

function Show-Summary {
    Write-Host "`n=================================="
    Write-Host "🎉 JoyAgent-JDGenie 启动完成！" -ForegroundColor Green
    Write-Host "=================================="
    
    Write-Host "`n🌐 访问地址：" -ForegroundColor Blue
    foreach ($service in $Script:Services) {
        Write-Host "• $($service.Name): $($service.Url)" -ForegroundColor Cyan
    }
    
    Write-Host "`n🔧 已配置的LLM服务：" -ForegroundColor Blue
    Write-Host "• DeepSeek: ***********************************" -ForegroundColor Cyan
    Write-Host "• 火山方舟: bcdd2ded-7352-4668-9fef-ebe31b56177a" -ForegroundColor Cyan
    Write-Host "• Serper搜索: 04d74f4080b9379d5875e04bccc3000aa8e7840f" -ForegroundColor Cyan
    
    Write-Host "`n💡 使用提示：" -ForegroundColor Blue
    Write-Host "• 已配置国内Docker镜像源，下载更快" -ForegroundColor Cyan
    Write-Host "• 日志文件: $Script:LogFile" -ForegroundColor Cyan
    Write-Host "• 故障排除: docker-compose logs -f" -ForegroundColor Cyan
    
    Write-Host "=================================="
}

#endregion

#region 主程序

function Main {
    try {
        Write-Host "🚀 JoyAgent-JDGenie 专业启动脚本" -ForegroundColor Blue
        Write-Host "=================================="
        
        Write-Log "启动脚本开始执行" "Info"
        Write-Log "参数: SkipMirrorConfig=$SkipMirrorConfig, Timeout=$Timeout" "Info"
        
        # 步骤1: 配置Docker镜像源
        Initialize-DockerMirrors
        
        # 步骤2: 重启Docker Desktop
        Restart-DockerDesktop
        
        # 步骤3: 等待Docker启动
        if (-not (Wait-ForDocker)) {
            throw "Docker启动失败"
        }
        
        # 步骤4: 测试Docker镜像源
        Test-DockerMirrors | Out-Null
        
        # 步骤5: 启动JoyAgent服务
        Start-JoyAgentServices
        
        # 步骤6: 等待服务启动
        Wait-ForServices | Out-Null
        
        # 步骤7: 显示服务状态
        Show-ServiceStatus
        
        # 步骤8: 显示总结信息
        Show-Summary
        
        # 询问是否打开浏览器
        $openBrowser = Read-Host "`n是否打开浏览器访问主界面？(Y/N)"
        if ($openBrowser -eq "Y" -or $openBrowser -eq "y") {
            Start-Process "http://localhost:3000"
        }
        
        Write-Log "启动脚本执行完成" "Success"
    }
    catch {
        Write-Log "启动脚本执行失败: $($_.Exception.Message)" "Error"
        Write-Host "`n❌ 启动失败，请查看日志文件: $Script:LogFile" -ForegroundColor Red
        exit 1
    }
    finally {
        Read-Host "`n按回车键退出..."
    }
}

# 执行主程序
Main

#endregion
