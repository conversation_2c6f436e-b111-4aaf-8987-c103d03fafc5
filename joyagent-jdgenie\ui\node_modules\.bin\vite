#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/vite@6.3.5_@types+node@24.1_ff2015c5d936c37d6a1ef2aef0e412ff/node_modules/vite/bin/node_modules:/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/vite@6.3.5_@types+node@24.1_ff2015c5d936c37d6a1ef2aef0e412ff/node_modules/vite/node_modules:/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/vite@6.3.5_@types+node@24.1_ff2015c5d936c37d6a1ef2aef0e412ff/node_modules:/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/vite@6.3.5_@types+node@24.1_ff2015c5d936c37d6a1ef2aef0e412ff/node_modules/vite/bin/node_modules:/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/vite@6.3.5_@types+node@24.1_ff2015c5d936c37d6a1ef2aef0e412ff/node_modules/vite/node_modules:/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/vite@6.3.5_@types+node@24.1_ff2015c5d936c37d6a1ef2aef0e412ff/node_modules:/proc/cygdrive/d/JDAGENT/joyagent-jdgenie/ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../vite/bin/vite.js" "$@"
fi
