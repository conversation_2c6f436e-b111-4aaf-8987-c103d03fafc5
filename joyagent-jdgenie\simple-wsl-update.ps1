# 简化WSL更新脚本
Write-Host "🔄 简化WSL更新脚本" -ForegroundColor Blue
Write-Host "=================================="

# 第1步：停止WSL
Write-Host "第1步：停止WSL..." -ForegroundColor Yellow
try {
    wsl --shutdown
    Write-Host "✅ WSL已停止" -ForegroundColor Green
    Start-Sleep -Seconds 3
} catch {
    Write-Host "⚠️ WSL停止可能失败" -ForegroundColor Yellow
}

# 第2步：尝试网络更新
Write-Host ""
Write-Host "第2步：尝试网络更新..." -ForegroundColor Yellow
try {
    Write-Host "正在从网络下载WSL更新..."
    wsl --update --web-download
    Write-Host "✅ 网络更新完成" -ForegroundColor Green
    $updateSuccess = $true
} catch {
    Write-Host "❌ 网络更新失败" -ForegroundColor Red
    $updateSuccess = $false
}

# 第3步：如果网络更新失败，打开Microsoft Store
if (-not $updateSuccess) {
    Write-Host ""
    Write-Host "第3步：打开Microsoft Store更新..." -ForegroundColor Yellow
    try {
        Start-Process "ms-windows-store://pdp/?productid=9P9TQF7MRM4R"
        Write-Host "✅ Microsoft Store已打开" -ForegroundColor Green
        Write-Host "请在Microsoft Store中点击'更新'按钮" -ForegroundColor Cyan
        Write-Host "更新完成后按回车键继续..." -ForegroundColor Cyan
        Read-Host
    } catch {
        Write-Host "❌ 无法打开Microsoft Store" -ForegroundColor Red
    }
}

# 第4步：验证WSL状态
Write-Host ""
Write-Host "第4步：验证WSL状态..." -ForegroundColor Yellow
try {
    $wslStatus = wsl --status 2>$null
    Write-Host "WSL状态："
    Write-Host $wslStatus
    Write-Host "✅ WSL状态检查完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 无法获取WSL状态" -ForegroundColor Yellow
}

# 第5步：尝试安装Ubuntu
Write-Host ""
Write-Host "第5步：尝试安装Ubuntu..." -ForegroundColor Yellow

$ubuntuVersions = @("Ubuntu-22.04", "Ubuntu-20.04", "Ubuntu")
$ubuntuInstalled = $false

foreach ($version in $ubuntuVersions) {
    Write-Host "尝试安装 $version..." -ForegroundColor Cyan
    
    try {
        wsl --install -d $version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $version 安装成功！" -ForegroundColor Green
            $ubuntuInstalled = $true
            break
        } else {
            Write-Host "❌ $version 安装失败" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $version 安装异常" -ForegroundColor Red
    }
    
    Start-Sleep -Seconds 2
}

if (-not $ubuntuInstalled) {
    Write-Host ""
    Write-Host "⚠️ Ubuntu自动安装失败" -ForegroundColor Yellow
    Write-Host "💡 可以通过Microsoft Store手动安装Ubuntu" -ForegroundColor Cyan
    Write-Host "或者跳过Ubuntu安装，直接使用Docker" -ForegroundColor Cyan
}

# 第6步：启动Docker Desktop
Write-Host ""
Write-Host "第6步：启动Docker Desktop..." -ForegroundColor Yellow

try {
    # 停止Docker Desktop
    Get-Process "Docker Desktop" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 3
    
    # 启动Docker Desktop
    Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    Write-Host "✅ Docker Desktop已启动" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Desktop启动失败" -ForegroundColor Red
}

Write-Host ""
Write-Host "=================================="
Write-Host "🎉 WSL更新和配置完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 下一步操作：" -ForegroundColor Blue
Write-Host "1. 等待Docker Desktop完全启动（约2-3分钟）" -ForegroundColor Cyan
Write-Host "2. 运行: docker info 验证Docker" -ForegroundColor Cyan
Write-Host "3. 运行: docker-start.sh 启动JoyAgent-JDGenie" -ForegroundColor Cyan
Write-Host ""
Write-Host "📂 项目目录: D:\JDAGENT\joyagent-jdgenie" -ForegroundColor Blue
Write-Host "🌐 访问地址: http://localhost:3000" -ForegroundColor Blue
Write-Host "=================================="

Read-Host "按回车键继续..."
