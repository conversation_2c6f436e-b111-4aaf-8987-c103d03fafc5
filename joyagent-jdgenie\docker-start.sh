#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🐳 JoyAgent-JDGenie Docker 启动脚本${NC}"
echo "=================================="

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker 未安装${NC}"
        echo -e "${YELLOW}请先安装Docker Desktop: https://www.docker.com/products/docker-desktop/${NC}"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose 未安装${NC}"
        echo -e "${YELLOW}请确保Docker Desktop已正确安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Docker 环境检查通过${NC}"
}

# 检查Docker服务是否运行
check_docker_service() {
    if ! docker info &> /dev/null; then
        echo -e "${RED}❌ Docker 服务未运行${NC}"
        echo -e "${YELLOW}请启动Docker Desktop${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Docker 服务运行正常${NC}"
}

# 停止现有容器
stop_existing() {
    echo -e "${BLUE}🛑 停止现有容器...${NC}"
    docker-compose down --remove-orphans
    
    # 清理悬空镜像
    echo -e "${BLUE}🧹 清理悬空镜像...${NC}"
    docker image prune -f
}

# 构建和启动服务
build_and_start() {
    echo -e "${BLUE}🔨 构建Docker镜像...${NC}"
    echo "这可能需要几分钟时间，请耐心等待..."
    
    if docker-compose build --no-cache; then
        echo -e "${GREEN}✅ 镜像构建成功${NC}"
    else
        echo -e "${RED}❌ 镜像构建失败${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}🚀 启动服务...${NC}"
    if docker-compose up -d; then
        echo -e "${GREEN}✅ 服务启动成功${NC}"
    else
        echo -e "${RED}❌ 服务启动失败${NC}"
        exit 1
    fi
}

# 等待服务启动
wait_for_services() {
    echo -e "${BLUE}⏳ 等待服务启动...${NC}"
    
    local max_attempts=60
    local attempt=0
    local services_ready=false
    
    while [ $attempt -lt $max_attempts ] && [ "$services_ready" = false ]; do
        attempt=$((attempt + 1))
        echo -ne "\r${BLUE}检查服务状态... ($attempt/$max_attempts)${NC}"
        
        # 检查容器健康状态
        if docker-compose ps | grep -q "healthy"; then
            services_ready=true
        fi
        
        sleep 5
    done
    
    echo ""
    
    if [ "$services_ready" = true ]; then
        echo -e "${GREEN}✅ 服务启动完成${NC}"
    else
        echo -e "${YELLOW}⚠️ 服务启动可能需要更多时间${NC}"
    fi
}

# 显示服务状态
show_status() {
    echo ""
    echo "=================================="
    echo -e "${GREEN}🎉 JoyAgent-JDGenie 启动完成！${NC}"
    echo "=================================="
    
    echo -e "${BLUE}📊 容器状态：${NC}"
    docker-compose ps
    
    echo ""
    echo -e "${BLUE}🌐 服务访问地址：${NC}"
    echo -e " 🎯 主界面: ${GREEN}http://localhost:3000${NC}"
    echo -e " 🔧 后端API: ${GREEN}http://localhost:8080${NC}"
    echo -e " 🛠️ 工具服务: ${GREEN}http://localhost:1601${NC}"
    echo -e " 🔌 MCP客户端: ${GREEN}http://localhost:8188${NC}"
    
    echo ""
    echo "=================================="
    echo -e "${YELLOW}💡 常用命令：${NC}"
    echo -e " 查看日志: ${GREEN}docker-compose logs -f${NC}"
    echo -e " 停止服务: ${GREEN}docker-compose down${NC}"
    echo -e " 重启服务: ${GREEN}docker-compose restart${NC}"
    echo -e " 查看状态: ${GREEN}docker-compose ps${NC}"
    echo "=================================="
}

# 主函数
main() {
    check_docker
    check_docker_service
    stop_existing
    build_and_start
    wait_for_services
    show_status
    
    echo -e "${GREEN}🎉 启动完成！请访问 http://localhost:3000 开始使用${NC}"
}

# 运行主函数
main "$@"
