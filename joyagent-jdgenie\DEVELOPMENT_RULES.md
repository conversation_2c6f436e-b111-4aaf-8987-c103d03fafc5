# 开发规则文档

## 端口配置规则

### 固定端口分配
- **前端端口**: 3000 (固定使用，不得变更)
- **后端端口**: 8081 (确定后保持一致，不得随意变更)
- **当前配置**: 前端 3000，后端 8081

### 端口占用处理
- 如遇端口占用情况，必须解决占用问题，而非更改端口
- 可使用以下命令检查端口占用：
  ```powershell
  netstat -ano | findstr :3000
  netstat -ano | findstr :8081
  ```
- 终止占用进程：
  ```powershell
  taskkill /PID <进程ID> /F
  ```

## 问题解决原则

### 核心原则
**遇到错误或问题强烈要求直接正面解决问题，不要逃避不要走捷径**

### 具体要求
1. **直面问题**: 不回避任何技术难题，必须找到根本原因
2. **系统性解决**: 考虑问题的全局影响，避免局部修复导致其他问题
3. **彻底修复**: 确保问题得到根本性解决，而非临时性缓解
4. **文档记录**: 重要问题的解决方案必须记录在案
5. **预防措施**: 在解决问题的同时，考虑如何避免类似问题再次发生
6、要求执行完任务后必须主动测试成功后才能交付

### 常见问题类型及处理方式

#### 网络连接问题
- 检查端口配置一致性
- 验证CORS设置
- 确认代理配置正确性

#### API错误处理
- 区分业务逻辑错误(400)和系统错误(500)
- 提供清晰的错误信息
- 实现适当的重试机制

#### 前后端通信
- 确保API端点一致
- 验证请求格式和响应格式
- 检查认证和授权流程

## 代码审查检查清单

### 连接配置检查
- [ ] 前端代理配置指向正确的后端端口
- [ ] 后端CORS配置包含所有必要的前端端口
- [ ] API客户端baseURL配置正确
- [ ] 环境变量配置一致

### 错误处理检查
- [ ] HTTP状态码使用正确
- [ ] 错误信息清晰明确
- [ ] 异常处理覆盖完整
- [ ] 日志记录详细准确

### 安全性检查
- [ ] 认证流程完整
- [ ] 授权验证正确
- [ ] 敏感信息保护
- [ ] 速率限制合理

---

**最后更新**: 2025-07-30
**维护者**: 开发团队