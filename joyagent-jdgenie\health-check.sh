#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🏥 JoyAgent-JDGenie 健康检查${NC}"
echo "=================================="

# 检查容器状态
echo -e "${BLUE}📦 容器状态检查${NC}"
docker-compose ps

echo ""
echo -e "${BLUE}🌐 服务连通性检查${NC}"

# 定义服务检查函数
check_service() {
    local name=$1
    local url=$2
    local timeout=${3:-5}
    
    if curl -s --max-time $timeout "$url" > /dev/null 2>&1; then
        echo -e "✅ $name: ${GREEN}正常${NC} ($url)"
        return 0
    else
        echo -e "❌ $name: ${RED}异常${NC} ($url)"
        return 1
    fi
}

# 检查各个服务
services_ok=0
total_services=4

check_service "前端服务" "http://localhost:3000" && ((services_ok++))
check_service "后端服务" "http://localhost:8080/web/health" && ((services_ok++))
check_service "工具服务" "http://localhost:1601" && ((services_ok++))
check_service "MCP客户端" "http://localhost:8188" && ((services_ok++))

echo ""
echo "=================================="
echo -e "${BLUE}📊 检查结果汇总${NC}"
echo "服务状态: $services_ok/$total_services 正常"

if [ $services_ok -eq $total_services ]; then
    echo -e "${GREEN}🎉 所有服务运行正常！${NC}"
    echo -e "主界面访问: ${GREEN}http://localhost:3000${NC}"
elif [ $services_ok -gt 0 ]; then
    echo -e "${YELLOW}⚠️ 部分服务异常，请检查日志${NC}"
    echo "查看日志命令: docker-compose logs -f"
else
    echo -e "${RED}❌ 所有服务都无法访问${NC}"
    echo "请检查Docker容器是否正常启动"
fi

echo ""
echo -e "${BLUE}💾 资源使用情况${NC}"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

echo ""
echo -e "${BLUE}📝 最近日志（最后10行）${NC}"
docker-compose logs --tail=10

echo "=================================="
