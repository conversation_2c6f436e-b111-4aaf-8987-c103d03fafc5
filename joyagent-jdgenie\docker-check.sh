#!/bin/bash

echo "🔍 Docker环境检查脚本"
echo "=================================="

# 检查Docker版本
echo "📦 检查Docker版本..."
if command -v docker &> /dev/null; then
    docker --version
    echo "✅ Docker已安装"
else
    echo "❌ Docker未安装"
    exit 1
fi

# 检查Docker Compose版本
echo ""
echo "🔧 检查Docker Compose版本..."
if command -v docker-compose &> /dev/null; then
    docker-compose --version
    echo "✅ Docker Compose已安装"
else
    echo "❌ Docker Compose未安装"
    exit 1
fi

# 检查Docker服务状态
echo ""
echo "🚀 检查Docker服务状态..."
if docker info > /dev/null 2>&1; then
    echo "✅ Docker服务运行正常"
    echo ""
    echo "📊 Docker系统信息:"
    docker info | head -10
else
    echo "❌ Docker服务未运行"
    echo "💡 请启动Docker Desktop"
    exit 1
fi

# 检查可用资源
echo ""
echo "💾 系统资源检查..."
echo "可用内存: $(free -h 2>/dev/null | grep Mem | awk '{print $7}' || echo '无法检测')"
echo "磁盘空间: $(df -h . | tail -1 | awk '{print $4}')"

echo ""
echo "🎉 Docker环境检查完成！"
echo "=================================="
