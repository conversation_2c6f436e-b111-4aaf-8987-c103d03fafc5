#!/bin/bash

echo "🔄 Docker重启和测试脚本"
echo "=================================="

echo "1. 停止Docker Desktop..."
taskkill /F /IM "Docker Desktop.exe" 2>/dev/null || echo "Docker Desktop未运行"
sleep 5

echo "2. 启动Docker Desktop..."
start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
echo "Docker Desktop启动中..."

echo "3. 等待Docker Engine就绪..."
max_attempts=60
attempt=0

while [ $attempt -lt $max_attempts ]; do
    attempt=$((attempt + 1))
    echo -ne "\r检查Docker状态... ($attempt/$max_attempts) "
    
    if timeout 5 docker info > /dev/null 2>&1; then
        echo ""
        echo "✅ Docker Engine已就绪！"
        break
    fi
    
    sleep 3
done

if [ $attempt -eq $max_attempts ]; then
    echo ""
    echo "❌ Docker启动超时"
    exit 1
fi

echo ""
echo "4. 测试Docker功能..."
echo "Docker版本: $(docker --version)"
echo "Docker Compose版本: $(docker-compose --version)"

echo ""
echo "5. 测试简单容器..."
if docker run --rm hello-world > /dev/null 2>&1; then
    echo "✅ Docker容器测试成功"
else
    echo "❌ Docker容器测试失败"
fi

echo ""
echo "=================================="
echo "🎉 Docker已准备就绪，可以开始构建JoyAgent-JDGenie！"
echo ""
echo "💡 下一步操作："
echo "   ./docker-start.sh"
echo "=================================="
