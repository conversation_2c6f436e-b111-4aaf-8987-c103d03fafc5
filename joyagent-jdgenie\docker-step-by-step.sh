#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🐳 JoyAgent-JDGenie 分步骤Docker启动${NC}"
echo "=================================="

# 步骤1: 环境检查
echo -e "${BLUE}步骤1: 环境检查${NC}"
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请启动Docker Desktop${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker环境正常${NC}"

# 步骤2: 清理旧容器
echo -e "${BLUE}步骤2: 清理旧容器和镜像${NC}"
docker-compose down --remove-orphans
docker system prune -f
echo -e "${GREEN}✅ 清理完成${NC}"

# 步骤3: 构建镜像
echo -e "${BLUE}步骤3: 构建Docker镜像${NC}"
echo "这是最耗时的步骤，请耐心等待..."
if docker-compose build --no-cache; then
    echo -e "${GREEN}✅ 镜像构建成功${NC}"
else
    echo -e "${RED}❌ 镜像构建失败${NC}"
    exit 1
fi

# 步骤4: 启动容器
echo -e "${BLUE}步骤4: 启动容器${NC}"
if docker-compose up -d; then
    echo -e "${GREEN}✅ 容器启动成功${NC}"
else
    echo -e "${RED}❌ 容器启动失败${NC}"
    exit 1
fi

# 步骤5: 等待服务就绪
echo -e "${BLUE}步骤5: 等待服务就绪${NC}"
echo "正在等待所有服务启动..."

max_wait=120
wait_time=0
services_ready=false

while [ $wait_time -lt $max_wait ] && [ "$services_ready" = false ]; do
    wait_time=$((wait_time + 5))
    echo -ne "\r等待中... ${wait_time}s/${max_wait}s"
    
    # 检查关键服务
    if curl -s http://localhost:3000 > /dev/null && \
       curl -s http://localhost:8080/web/health > /dev/null && \
       curl -s http://localhost:1601 > /dev/null; then
        services_ready=true
    fi
    
    sleep 5
done

echo ""

if [ "$services_ready" = true ]; then
    echo -e "${GREEN}✅ 所有服务已就绪${NC}"
else
    echo -e "${YELLOW}⚠️ 部分服务可能仍在启动中${NC}"
fi

# 步骤6: 显示状态
echo -e "${BLUE}步骤6: 服务状态检查${NC}"
echo "=================================="
docker-compose ps
echo "=================================="

echo -e "${GREEN}🎉 启动完成！${NC}"
echo -e "访问地址: ${GREEN}http://localhost:3000${NC}"

echo ""
echo -e "${YELLOW}💡 有用的命令:${NC}"
echo "查看日志: docker-compose logs -f"
echo "停止服务: docker-compose down"
echo "重启服务: docker-compose restart"
