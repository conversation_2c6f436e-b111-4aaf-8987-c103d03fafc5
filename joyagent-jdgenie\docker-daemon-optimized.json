{"builder": {"gc": {"defaultKeepStorage": "20GB", "enabled": true}}, "experimental": false, "log-driver": "json-file", "log-opts": {"max-size": "100m", "max-file": "3"}, "storage-driver": "windowsfilter", "registry-mirrors": ["https://docker.m.daocloud.io", "https://registry.docker-cn.com"], "insecure-registries": [], "debug": false, "hosts": ["npipe://"], "max-concurrent-downloads": 3, "max-concurrent-uploads": 5, "default-shm-size": "64M"}