# Ubuntu WSL安装修复脚本
Write-Host "🐧 Ubuntu WSL安装修复脚本" -ForegroundColor Blue
Write-Host "=================================="

# 检查管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "❌ 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按回车键退出..."
    exit 1
}

Write-Host "✅ 管理员权限确认" -ForegroundColor Green

# 第1步：检查当前WSL状态
Write-Host ""
Write-Host "第1步：检查当前WSL状态..." -ForegroundColor Yellow
try {
    $wslList = wsl --list --verbose 2>$null
    Write-Host "当前已安装的发行版："
    Write-Host $wslList
} catch {
    Write-Host "⚠️ 无法获取WSL列表" -ForegroundColor Yellow
}

# 第2步：重置WSL
Write-Host ""
Write-Host "第2步：重置WSL..." -ForegroundColor Yellow
try {
    wsl --shutdown
    Write-Host "✅ WSL已关闭" -ForegroundColor Green
    Start-Sleep -Seconds 5
} catch {
    Write-Host "⚠️ WSL关闭可能失败" -ForegroundColor Yellow
}

# 第3步：尝试多种Ubuntu版本
Write-Host ""
Write-Host "第3步：尝试安装Ubuntu..." -ForegroundColor Yellow

$ubuntuVersions = @(
    @{Name="Ubuntu-22.04"; DisplayName="Ubuntu 22.04 LTS"},
    @{Name="Ubuntu-20.04"; DisplayName="Ubuntu 20.04 LTS"},
    @{Name="Ubuntu-24.04"; DisplayName="Ubuntu 24.04 LTS"},
    @{Name="Ubuntu"; DisplayName="Ubuntu (最新版)"}
)

$installed = $false

foreach ($version in $ubuntuVersions) {
    Write-Host "尝试安装 $($version.DisplayName)..." -ForegroundColor Cyan
    
    try {
        $process = Start-Process -FilePath "wsl" -ArgumentList "--install", "-d", $version.Name -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Host "✅ $($version.DisplayName) 安装成功！" -ForegroundColor Green
            $installed = $true
            break
        } else {
            Write-Host "❌ $($version.DisplayName) 安装失败，错误代码: $($process.ExitCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $($version.DisplayName) 安装异常: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Seconds 2
}

# 第4步：如果自动安装失败，提供手动安装选项
if (-not $installed) {
    Write-Host ""
    Write-Host "第4步：自动安装失败，尝试手动方式..." -ForegroundColor Yellow
    
    Write-Host "方案1：通过Microsoft Store安装" -ForegroundColor Cyan
    Write-Host "正在打开Microsoft Store..." -ForegroundColor Cyan
    try {
        Start-Process "ms-windows-store://pdp/?productid=9PDXGNCFSCZV"
        Write-Host "✅ Microsoft Store已打开，请手动安装Ubuntu" -ForegroundColor Green
    } catch {
        Write-Host "❌ 无法打开Microsoft Store" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "方案2：下载离线安装包" -ForegroundColor Cyan
    Write-Host "Ubuntu 22.04下载链接: https://aka.ms/wslubuntu2204" -ForegroundColor Cyan
    Write-Host "下载后双击安装即可" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "方案3：跳过Ubuntu安装" -ForegroundColor Cyan
    Write-Host "Docker Desktop可以在没有Linux发行版的情况下运行" -ForegroundColor Cyan
}

# 第5步：验证安装结果
Write-Host ""
Write-Host "第5步：验证安装结果..." -ForegroundColor Yellow
try {
    $wslListAfter = wsl --list --verbose 2>$null
    Write-Host "安装后的发行版列表："
    Write-Host $wslListAfter
    
    if ($wslListAfter -match "Ubuntu") {
        Write-Host "✅ Ubuntu安装成功！" -ForegroundColor Green
        $installed = $true
    } else {
        Write-Host "❌ 未检测到Ubuntu安装" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠️ 无法验证安装结果" -ForegroundColor Yellow
}

# 第6步：配置Docker Desktop
Write-Host ""
Write-Host "第6步：准备启动Docker Desktop..." -ForegroundColor Yellow

# 停止Docker Desktop
try {
    Get-Process "Docker Desktop" -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "Docker Desktop已停止" -ForegroundColor Green
} catch {
    Write-Host "Docker Desktop未运行或停止失败" -ForegroundColor Yellow
}

Start-Sleep -Seconds 3

# 启动Docker Desktop
try {
    Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    Write-Host "✅ Docker Desktop已启动" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Desktop启动失败" -ForegroundColor Red
}

Write-Host ""
Write-Host "=================================="
if ($installed) {
    Write-Host "🎉 Ubuntu安装成功！" -ForegroundColor Green
} else {
    Write-Host "⚠️ Ubuntu安装未完成，但可以继续使用Docker" -ForegroundColor Yellow
}
Write-Host ""
Write-Host "📋 下一步操作：" -ForegroundColor Blue
Write-Host "1. 等待Docker Desktop完全启动" -ForegroundColor Cyan
Write-Host "2. 运行: docker info 验证Docker" -ForegroundColor Cyan
Write-Host "3. 运行: docker-start.sh 启动JoyAgent-JDGenie" -ForegroundColor Cyan
Write-Host "=================================="

Read-Host "按回车键继续..."
