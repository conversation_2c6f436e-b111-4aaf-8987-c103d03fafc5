#!/bin/bash

echo "🔍 Docker构建监控脚本"
echo "=================================="

while true; do
    echo "时间: $(date '+%H:%M:%S')"
    
    # 检查容器状态
    echo "=== 容器状态 ==="
    docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "无容器运行"
    
    echo ""
    echo "=== 镜像状态 ==="
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | head -5 2>/dev/null || echo "无镜像"
    
    echo ""
    echo "=== 系统资源 ==="
    docker system df 2>/dev/null || echo "无法获取系统信息"
    
    echo "=================================="
    sleep 10
done
